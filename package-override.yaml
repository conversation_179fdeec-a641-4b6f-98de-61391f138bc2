Parameters:
  ProductName:
    Type: String
    Default: 'step-up-auth-svc'
  DefaultRegion:
    Type: String
  DefaultSessionExpiredInSeconds:
    Type: String
    Default: 300
  MaxSessionExpiredInSeconds:
    Type: String
    Default: 900
  LinkDeviceThresholdInDays:
    Type: String
  DeviceBioEnrollThresholdInDays:
    Type: String
  DeviceBioEnrollmentQueue:
    Type: String
  ClientProfileIngressApiKey:
    Type: String

Resources:
  TaskDefinition:
    Properties:
      ContainerDefinitions:
        - Name: !Sub ${ResourceName}
          Environment:
            - Name: DEFAULT_REGION
              Value: !Ref DefaultRegion
            - Name: DEFAULT_SESSION_EXPIRED_IN_SECONDS
              Value: !Ref DefaultSessionExpiredInSeconds
            - Name: MAX_SESSION_EXPIRED_IN_SECONDS
              Value: !Ref MaxSessionExpiredInSeconds
            - Name: LINK_DEVICE_THRESHOLD_IN_DAYS
              Value: !Ref LinkDeviceThresholdInDays
            - Name: DEVICE_BIO_ENROLL_THRESHOLD_IN_DAYS
              Value: !Ref DeviceBioEnrollThresholdInDays
            - Name: DEVICE_BIO_ENROLLMENT_SQS
              Value: !Ref DeviceBioEnrollmentQueue
          Secrets:
            - Name: CLIENT_PROFILE_INGRESS_DOMAIN
              ValueFrom: !Sub 'arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/tp/common/integration/apigw/client-profile-ingress/domain-name'
            - Name: CLIENT_PROFILE_INGRESS_API_KEY
              ValueFrom: !Sub 'arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter${ClientProfileIngressApiKey}'
