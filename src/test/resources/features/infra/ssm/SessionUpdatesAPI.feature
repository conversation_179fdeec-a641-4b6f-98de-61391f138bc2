Feature: Updates of a Step-Up Session
  As a factor
  I want to update my authentication result to the Step-Up Session records
  So that I can make a request to this service to update it

  Scenario: Check if the Step-Up Session existing in database
    Given A Step-Up Session is not presented in our database
    When The factor init a new request with this session with passed result
    Then Service should return a bad request with a specific error code

  Scenario: Check if inserting a new factor record and overall status successfully
    Given A Step-Up Session is presented but have not a Factor record yet in our database
    When The factor init a new request with this session with passed result
    Then Service should insert a new Factor record and update overall status of the Step-Up Session record

  Scenario: Check if the factor record update at second time successfully
    Given A Step-Up Session is presented and a Factor record updated first time in our database
    When The factor init a new request with this session with passed result
    Then Service should update the Factor record second time and update the Step-Up Session record

  Scenario: Check if a next factor looked up correctly
    Given A presented Step-Up Session have fallback rules and a Factor record updated first time in our database
    When The factor init a new request with this session with failed result
    Then Service should update the Factor and Session records with a correct next factor

  Scenario: Check if a fallback record inserted successfully or not
    Given A presented Step-Up Session have fallback rules and a primary factor record completed in our database
    When The factor init a new request with this session with passed result
    Then Service should update the fallback Factor and Session records successfully

  Scenario: Check if a maxAttempts in request validated or not
    Given A factor record presented in database with a specific maxAttempt value
    When The factor init a new request with this session with passed result
    Then Service should throw an exception indicating that the maximum number of attempts has not been expected