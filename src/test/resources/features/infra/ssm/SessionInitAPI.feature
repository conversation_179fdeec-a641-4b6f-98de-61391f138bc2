Feature: Initialization of a Step-Up Session
  As a client application
  I want to initialize a step-up authentication session
  So that I can authenticate users with various factors

  Scenario: Successfully initialize session with OTP factor and PROFILE_ID
    Given A valid profile exists in the system
    And The profile has valid phone number information
    When I initialize a step-up session with OTP factor and PRO<PERSON>LE_ID
    Then The session should be created successfully
    And The session should have OTP factor configured
    And The OTP configuration should be enriched with profile phone data
    And The session status should be IN_PROGRESS
    And The current factor should be OTP

  Scenario: Initialize session with OTP factor and PRO<PERSON>LE_ID but profile not found
    Given A profile does not exist in the system
    When I initialize a step-up session with OTP factor and PROFILE_ID
    Then The service should return an error indicating profile not found

  <PERSON><PERSON><PERSON>: Initialize session with OTP factor and PRO<PERSON>LE_ID but profile has no phone data
    Given A profile exists but has no phone number information
    When I initialize a step-up session with OTP factor and PROFILE_ID
    Then The service should return an error indicating invalid profile data