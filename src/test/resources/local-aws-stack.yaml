AWSTemplateFormatVersion: '2010-09-09'

Resources:
  InboxDynamoDBTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: step-up-auth-inbox
      KeySchema:
        - AttributeName: pk
          KeyType: HASH
      AttributeDefinitions:
        - AttributeName: pk
          AttributeType: S
      BillingMode: PAY_PER_REQUEST

  DeviceBioEnrollmentQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: tp-step-up-device-bio-enrollment-sqs
      DelaySeconds: 30

  StepUpAggregationDynamoDBTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: step-up-aggregation
      KeySchema:
        - AttributeName: pk
          KeyType: HASH
        - AttributeName: sk
          KeyType: RANGE
      AttributeDefinitions:
        - AttributeName: pk
          AttributeType: S
        - AttributeName: sk
          AttributeType: S
      BillingMode: PAY_PER_REQUEST
