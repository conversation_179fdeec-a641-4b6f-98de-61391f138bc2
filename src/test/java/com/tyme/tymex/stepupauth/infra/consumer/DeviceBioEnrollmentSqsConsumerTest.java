package com.tyme.tymex.stepupauth.infra.consumer;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.verify;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tyme.tymex.stepupauth.helper.TestHelper;
import com.tyme.tymex.stepupauth.infra.dynamodb.domain.Inbox;
import com.tyme.tymex.stepupauth.infra.event.DeviceBioEnrollmentDetail;
import com.tyme.tymex.stepupauth.infra.event.Event;
import com.tyme.tymex.stepupauth.infra.exception.DomainException;
import com.tyme.tymex.stepupauth.infra.exception.model.SystemException;
import com.tyme.tymex.stepupauth.service.DeviceBioEnrollmentService;
import com.tyme.tymex.stepupauth.service.InboxService;
import com.tyme.tymex.stepupauth.utils.GlobalUtils;
import io.awspring.cloud.sqs.listener.QueueMessageVisibility;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import java.util.Set;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.messaging.MessagingException;

@ExtendWith(MockitoExtension.class)
class DeviceBioEnrollmentSqsConsumerTest {

  @Spy
  ObjectMapper objectMapper = new ObjectMapper();
  @Mock
  ConstraintViolation<Event<DeviceBioEnrollmentDetail>> violation;
  @Mock
  QueueMessageVisibility queueMessageVisibility;
  @Mock
  private InboxService inboxService;
  @Mock
  private DeviceBioEnrollmentService deviceBioEnrollmentService;
  @Mock
  private Validator validator;
  @InjectMocks
  private DeviceBioEnrollmentSqsConsumer deviceBioEnrollmentSqsConsumer;


  @Test
  void shouldProcessTheEvent_whenTheEventIsNotRead() throws JsonProcessingException {
    var event = TestHelper.createDeviceBioEnrollmentEvent();
    var message = createMessage(event);

    var inbox = createInbox(event, false);
    when(inboxService.writeEvent(any())).thenReturn(inbox);
    deviceBioEnrollmentSqsConsumer.consume(message);
    verify(inboxService, times(1)).writeEvent(event);
    verify(deviceBioEnrollmentService, times(1)).processDeviceBioEnrollmentEvent(event);

    verify(inboxService, times(1)).markAsRead(event);
  }

  @Test
  void shouldRecordException_whenProcessThrowException() throws JsonProcessingException {
    var event = TestHelper.createDeviceBioEnrollmentEvent();
    var message = createMessage(event);

    var inbox = createInbox(event, false);
    when(inboxService.writeEvent(any())).thenReturn(inbox);
    doThrow(new MessagingException("Sample Exception") {
    }).when(deviceBioEnrollmentService).processDeviceBioEnrollmentEvent(event);

    assertThrows(MessagingException.class, () -> deviceBioEnrollmentSqsConsumer.consume(message));
    verify(inboxService, times(1)).writeEvent(event);
    verify(inboxService, times(0)).markAsRead(event);
    verify(deviceBioEnrollmentService, times(1)).processDeviceBioEnrollmentEvent(event);
  }

  @Test
  void shouldNotProcessTheEvent_whenTheEventAlreadyRead() throws JsonProcessingException {
    var event = TestHelper.createDeviceBioEnrollmentEvent();
    var message = createMessage(event);

    var inbox = createInbox(event, true);
    when(inboxService.writeEvent(any())).thenReturn(inbox);
    deviceBioEnrollmentSqsConsumer.consume(message);
    verify(inboxService, times(1)).writeEvent(event);
    verify(inboxService, times(0)).markAsRead(event);
    verify(deviceBioEnrollmentService, times(0)).processDeviceBioEnrollmentEvent(event);
  }

  @Test
  void shouldThrowDomainException_whenEventPayloadIsInvalid() throws JsonProcessingException {
    var event = TestHelper.createDeviceBioEnrollmentEvent();
    var message = createMessage(event);
    when(validator.validate(event)).thenReturn(Set.of(violation));
    assertThrows(DomainException.class, () -> deviceBioEnrollmentSqsConsumer.consume(message));

    verify(validator, times(1)).validate(event);
    verify(inboxService, times(0)).writeEvent(event);
    verify(deviceBioEnrollmentService, times(0)).processDeviceBioEnrollmentEvent(event);
  }


  @Test
  void shouldThrowSystemException_whenCannotDeserializeTheEvent() throws JsonProcessingException {
    var event = TestHelper.createDeviceBioEnrollmentEvent();
    var message = createMessage(event);

    var javaType = objectMapper.getTypeFactory()
        .constructParametricType(Event.class, DeviceBioEnrollmentDetail.class);
    when(objectMapper.readValue(message, javaType))
        .thenThrow(new JsonProcessingException("Exception") {
        });

    assertThrows(SystemException.class, () -> deviceBioEnrollmentSqsConsumer.consume(message));
    verify(inboxService, times(0)).findEvent(event);
    verify(deviceBioEnrollmentService, times(0)).processDeviceBioEnrollmentEvent(event);
  }

  private String createMessage(Event<DeviceBioEnrollmentDetail> event)
      throws JsonProcessingException {
    return objectMapper.writeValueAsString(event);
  }

  private Inbox createInbox(Event<DeviceBioEnrollmentDetail> event, boolean isRead) {
    return Inbox.builder()
        .partitionKey(GlobalUtils.toInboxPk(event.getId()))
        .isRead(isRead)
        .note(StringUtils.EMPTY)
        .payload(StringUtils.EMPTY)
        .type(event.getType())
        .build();
  }


}