package com.tyme.tymex.stepupauth.infra.dynamodb.repo;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;

import com.tyme.tymex.stepupauth.infra.dynamodb.domain.Inbox;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.services.dynamodb.DynamoDbClient;

import java.util.Optional;
import java.util.function.Consumer;

@ExtendWith(MockitoExtension.class)
class InboxRepoImplTest {

  @Mock
  private DynamoDbTable<Inbox> dynamoInboxTable;

  @Mock
  private DynamoDbClient dynamoDbClient;

  @InjectMocks
  private InboxRepoImpl inboxRepository;

  @Test
  void shouldPutItemInDynamoDB() {
    Inbox inbox = Inbox.builder().partitionKey("test-key").build();
    inboxRepository.put(inbox);
    verify(dynamoInboxTable).putItem(inbox);
  }

  @Test
  void shouldUpdateItemInDynamoDB() {
    Inbox inbox = Inbox.builder().partitionKey("test-key").build();
    inboxRepository.update(inbox);
    verify(dynamoInboxTable).updateItem(inbox);
  }

  @Test
  void shouldFindItemByPartitionKey() {
    String partitionKey = "test-partition-key";
    Inbox expectedInbox = Inbox.builder().partitionKey(partitionKey).build();

    when(dynamoInboxTable.getItem(any(Consumer.class))).thenReturn(expectedInbox);

    Optional<Inbox> result = inboxRepository.findByPk(partitionKey);

    assertTrue(result.isPresent());
    assertEquals(partitionKey, result.get().getPartitionKey());
    verify(dynamoInboxTable).getItem(any(Consumer.class));
  }

  @Test
  void shouldReturnEmptyOptionalWhenItemNotFound() {
    String partitionKey = "non-existent-key";
    when(dynamoInboxTable.getItem(any(Consumer.class))).thenReturn(null);

    Optional<Inbox> result = inboxRepository.findByPk(partitionKey);

    assertFalse(result.isPresent());
    verify(dynamoInboxTable).getItem(any(Consumer.class));
  }
}