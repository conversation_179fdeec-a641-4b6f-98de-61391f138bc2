package com.tyme.tymex.stepupauth.infra.consumer;

import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.tyme.tymex.stepupauth.infra.exception.DomainException;
import com.tyme.tymex.stepupauth.infra.exception.model.ErrorCode;
import io.awspring.cloud.sqs.listener.QueueMessageVisibility;
import io.awspring.cloud.sqs.listener.SqsHeaders;
import io.awspring.cloud.sqs.listener.SqsHeaders.MessageSystemAttributes;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.GenericMessage;
import org.springframework.test.util.ReflectionTestUtils;

@ExtendWith(MockitoExtension.class)
class CustomAsyncSqsErrorHandlerTest {

  @InjectMocks
  CustomAsyncSqsErrorHandler asyncSqsErrorHandler;

  @Mock
  QueueMessageVisibility queueMessageVisibility;

  @BeforeEach
  void init() {
    ReflectionTestUtils.setField(asyncSqsErrorHandler, "backOffExponent", 3.0);
    ReflectionTestUtils.setField(asyncSqsErrorHandler, "backOffMultiplier", 0.5);


  }

  @Test
  void shouldExtendVisibilityTimeout1Seconds_At1stTry() {
    var ex = new DomainException(ErrorCode.UNKNOWN_ERROR);
    var headers = Map.of(SqsHeaders.SQS_VISIBILITY_TIMEOUT_HEADER, queueMessageVisibility,
        MessageSystemAttributes.SQS_APPROXIMATE_RECEIVE_COUNT, "1");

    Message<Object> message = new GenericMessage("", headers);
    asyncSqsErrorHandler.handle(message, ex);

    verify(queueMessageVisibility, times(1)).changeTo(1);
  }

  @Test
  void shouldExtendVisibilityTimeout32Seconds_At4thTry() {
    var ex = new DomainException(ErrorCode.UNKNOWN_ERROR);
    var headers = Map.of(SqsHeaders.SQS_VISIBILITY_TIMEOUT_HEADER, queueMessageVisibility,
        MessageSystemAttributes.SQS_APPROXIMATE_RECEIVE_COUNT, "4");

    Message<Object> message = new GenericMessage("", headers);
    asyncSqsErrorHandler.handle(message, ex);

    verify(queueMessageVisibility, times(1)).changeTo(32);
  }


}