package com.tyme.tymex.stepupauth.infra.dynamodb.repo;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

import com.tyme.tymex.stepupauth.infra.dynamodb.domain.StepUpEntity;
import java.util.Iterator;
import java.util.List;
import java.util.function.Consumer;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;
import software.amazon.awssdk.enhanced.dynamodb.model.GetItemEnhancedRequest;
import software.amazon.awssdk.enhanced.dynamodb.model.Page;
import software.amazon.awssdk.enhanced.dynamodb.model.PageIterable;
import software.amazon.awssdk.enhanced.dynamodb.model.PutItemEnhancedRequest;
import software.amazon.awssdk.services.dynamodb.DynamoDbClient;
import software.amazon.awssdk.services.dynamodb.model.TransactWriteItemsRequest;
import software.amazon.awssdk.services.dynamodb.model.TransactWriteItemsResponse;

@ExtendWith(MockitoExtension.class)
class StepUpRepoImplTest {

  @Mock
  DynamoDbClient dynamoDbClient;

  @Mock
  DynamoDbTable<StepUpEntity> dynamoDbStepUpTable;

  @InjectMocks
  StepUpRepoImpl stepUpRepo;

  @Test
  void queryByAuthId_success() {
    var stepUpAuthId = "sua-1";
    when(dynamoDbStepUpTable.query(any(Consumer.class))).thenReturn(
        new PageIterable<StepUpEntity>() {
          @Override
          public @NotNull Iterator<Page<StepUpEntity>> iterator() {
            return List.of(Page.builder(StepUpEntity.class).items(List.of(StepUpEntity.builder()
                .stepUpAuthId(stepUpAuthId).build())).build()).iterator();
          }
        });
    var result = stepUpRepo.queryByAuthId(stepUpAuthId).toList();
    Assertions.assertNotNull(result);
    Assertions.assertEquals(1, result.size());
    Assertions.assertEquals(stepUpAuthId, result.getFirst().getStepUpAuthId());
  }

  @Test
  void transactUpsertWithRollback_success() {
    when(dynamoDbStepUpTable.tableSchema()).thenReturn(TableSchema.fromClass(StepUpEntity.class));
    when(dynamoDbClient.transactWriteItems(any(TransactWriteItemsRequest.class))).thenReturn(
        TransactWriteItemsResponse.builder().build());
    Assertions.assertDoesNotThrow(
        () -> stepUpRepo.transactUpsertWithRollback(new StepUpEntity(), new StepUpEntity()));
  }

  @Test
  void transactUpsertWithRollback_fail() {
    Exception ex = Assertions.assertThrows(IllegalArgumentException.class,
        () -> stepUpRepo.transactUpsertWithRollback());
    Assertions.assertEquals("At least two entities are required for upsert operation",
        ex.getMessage());
  }

  @Test
  void saveStepUp_success() {
    doNothing().when(dynamoDbStepUpTable).putItem(any(PutItemEnhancedRequest.class));
    Assertions.assertDoesNotThrow(() -> stepUpRepo.saveStepUp(new StepUpEntity()));
  }

  @Test
  void findStepUpSessionByAuthId_success() {
    when(dynamoDbStepUpTable.getItem(any(GetItemEnhancedRequest.class))).thenReturn(
        new StepUpEntity());

    var result = stepUpRepo.findStepUpSessionByAuthId("authId");

    Assertions.assertNotNull(result);
  }

}