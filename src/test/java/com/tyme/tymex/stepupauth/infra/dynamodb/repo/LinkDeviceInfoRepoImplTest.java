package com.tyme.tymex.stepupauth.infra.dynamodb.repo;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.tyme.tymex.stepupauth.infra.dynamodb.domain.LinkDeviceInfo;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.model.GetItemEnhancedRequest;

@ExtendWith(MockitoExtension.class)
class LinkDeviceInfoRepoImplTest {

  @Mock
  DynamoDbTable<LinkDeviceInfo> linkDeviceInfoTable;

  @InjectMocks
  LinkDeviceInfoRepoImpl linkDeviceInfoRepo;

  @Test
  void testFindLinkDeviceInfoByProfileIdAndAppId_success() {
    // Arrange
    LinkDeviceInfo mockLinkDeviceInfo = new LinkDeviceInfo();
    when(linkDeviceInfoTable.getItem(any(GetItemEnhancedRequest.class)))
        .thenReturn(mockLinkDeviceInfo);

    Optional<LinkDeviceInfo> result =
        linkDeviceInfoRepo.findLinkDeviceInfoByProfileIdAndAppId("testProfileId", "testAppId");

    assertTrue(result.isPresent());
    assertEquals(mockLinkDeviceInfo, result.get());
    verify(linkDeviceInfoTable).getItem(any(GetItemEnhancedRequest.class));
  }

  @Test
  void testFindLinkDeviceInfoByProfileIdAndAppId_notFound() {
    when(linkDeviceInfoTable.getItem(any(GetItemEnhancedRequest.class)))
        .thenReturn(null);

    Optional<LinkDeviceInfo> result =
        linkDeviceInfoRepo.findLinkDeviceInfoByProfileIdAndAppId("testProfileId", "testAppId");

    assertFalse(result.isPresent());
    verify(linkDeviceInfoTable).getItem(any(GetItemEnhancedRequest.class));
  }
}