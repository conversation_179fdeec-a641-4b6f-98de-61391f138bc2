package com.tyme.tymex.stepupauth.infra.dynamodb.repo;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.tyme.tymex.stepupauth.infra.dynamodb.domain.StepUpAuthDeviceBioEnrollment;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.model.GetItemEnhancedRequest;

@ExtendWith(MockitoExtension.class)
class DeviceBioEnrollmentRepoImplTest {

  @Mock
  DynamoDbTable<StepUpAuthDeviceBioEnrollment> stepUpAuthDeviceBioEnrollmentDynamoDbTable;

  @InjectMocks
  DeviceBioEnrollmentRepoImpl deviceBioEnrollmentRepo;

  @Test
  void testFindDeviceBioEnrollmentByProfileId_success() {
    when(stepUpAuthDeviceBioEnrollmentDynamoDbTable.getItem(
        any(GetItemEnhancedRequest.class))).thenReturn(
        new StepUpAuthDeviceBioEnrollment());
    Optional<StepUpAuthDeviceBioEnrollment> result = deviceBioEnrollmentRepo.findDeviceBioEnrollmentByProfileId(
        "testProfileId");

    assertTrue(result.isPresent());
    verify(stepUpAuthDeviceBioEnrollmentDynamoDbTable).getItem(any(GetItemEnhancedRequest.class));
  }

  @Test
  void saveDeviceBioEnrollmentRecord_success() {
    deviceBioEnrollmentRepo.saveDeviceBioEnrollment(new StepUpAuthDeviceBioEnrollment());
    verify(stepUpAuthDeviceBioEnrollmentDynamoDbTable).updateItem(
        any(StepUpAuthDeviceBioEnrollment.class));
  }


}