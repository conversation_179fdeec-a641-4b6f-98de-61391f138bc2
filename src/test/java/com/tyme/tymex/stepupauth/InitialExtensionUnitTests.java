package com.tyme.tymex.stepupauth;

import lombok.extern.log4j.Log4j2;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.boot.SpringApplication;

@Log4j2
class InitialExtensionUnitTests extends StepUpAuthApplicationTestsBase {

    @Test
    void giveValidLocalStack_thenShouldSuccessfullyStart() {
        log.info("verify localStack");
        Assertions.assertTrue(InitialExtension.localstackContainer.isRunning());
    }

    @Test
    void getValidArgs_thenShouldSuccessfullyFacialCentralApplicationStart() {
        try (MockedStatic<SpringApplication> springApplication = Mockito.mockStatic(SpringApplication.class)) {
            StepUpAuthApplication.main(new String[]{});
            springApplication.when(() -> SpringApplication.run(StepUpAuthApplication.class, new String[]{}))
                    .thenCallRealMethod();
        }
        Assertions.assertTrue(true);
    }

}
