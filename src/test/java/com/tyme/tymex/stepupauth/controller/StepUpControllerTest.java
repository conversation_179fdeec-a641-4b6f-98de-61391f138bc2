package com.tyme.tymex.stepupauth.controller;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tyme.tymex.stepupauth.StepUpAuthApplicationTestsBase;
import com.tyme.tymex.stepupauth.controller.domain.InitStepUpSessionDto;
import com.tyme.tymex.stepupauth.controller.domain.InitStepUpSessionDto.AuthFactorRule;
import com.tyme.tymex.stepupauth.controller.domain.StepUpSessionResponse;
import com.tyme.tymex.stepupauth.controller.domain.StepUpUpdateRequest;
import com.tyme.tymex.stepupauth.controller.domain.StepUpVerificationRequest;
import com.tyme.tymex.stepupauth.controller.domain.StepUpVerificationResponse;
import com.tyme.tymex.stepupauth.domain.AuthFactor;
import com.tyme.tymex.stepupauth.domain.DeviceBioAuthConfig;
import com.tyme.tymex.stepupauth.domain.FactorStatus;
import com.tyme.tymex.stepupauth.domain.IdentifierType;
import com.tyme.tymex.stepupauth.domain.OtpAuthConfig;
import com.tyme.tymex.stepupauth.domain.PasscodeAuthConfig;
import com.tyme.tymex.stepupauth.helper.TestHelper;
import com.tyme.tymex.stepupauth.infra.exception.model.ErrorCode;
import com.tyme.tymex.stepupauth.service.StepUpService;
import java.util.EnumMap;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Stream;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;


class StepUpControllerTest extends StepUpAuthApplicationTestsBase {

  @MockBean
  private StepUpService stepUpService;

  @Autowired
  private MockMvc mockMvc;

  @Autowired
  private ObjectMapper objectMapper;

  Map<AuthFactor, Object> validOTPFactorConfigs = Map.of(AuthFactor.OTP,
      OtpAuthConfig.builder().dialCode("+63").cellphone("+************").build());

  @Test
  void initializeStepUpSession_givenInvalidOTPConfig_InvalidCellphone_thenReturn400()
      throws Exception {
    InitStepUpSessionDto request = InitStepUpSessionDto.builder()
        .authFactorRules(List.of(AuthFactorRule.builder().factor(AuthFactor.OTP).build()))
        .flowId(UUID.randomUUID().toString())
        .flowName("NAME").identifierId(UUID.randomUUID().toString())
        .identifierType(IdentifierType.PHONE)
        .authConfig(Map.of(AuthFactor.OTP, OtpAuthConfig.builder().dialCode("+63").build()))
        .build();
    mockMvc.perform(post("/internal/step-up/init").contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON).content(objectMapper.writeValueAsString(request)))
        .andExpect(status().isBadRequest());
  }

  @Test
  void initializeStepUpSession_givenInvalidOTPConfig_InvalidDialCode_thenReturn400()
      throws Exception {
    InitStepUpSessionDto request = InitStepUpSessionDto.builder()
        .authFactorRules(List.of(AuthFactorRule.builder().factor(AuthFactor.OTP).build()))
        .flowId(UUID.randomUUID().toString())
        .flowName("NAME").identifierId(UUID.randomUUID().toString())
        .identifierType(IdentifierType.PHONE)
        .authConfig(
            Map.of(AuthFactor.OTP, OtpAuthConfig.builder().cellphone("+************").build()))
        .build();
    mockMvc.perform(post("/internal/step-up/init").contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON).content(objectMapper.writeValueAsString(request)))
        .andExpect(status().isBadRequest());
  }

  @Test
  void initializeStepUpSession_givenInvalidOTPConfigNull_thenReturn400() throws Exception {
    InitStepUpSessionDto request = InitStepUpSessionDto.builder()
        .authFactorRules(List.of(AuthFactorRule.builder().factor(AuthFactor.OTP).build()))
        .flowId(UUID.randomUUID().toString())
        .flowName("NAME").identifierId(UUID.randomUUID().toString())
        .identifierType(IdentifierType.PHONE).build();
    mockMvc.perform(post("/internal/step-up/init").contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON).content(objectMapper.writeValueAsString(request)))
        .andExpect(status().isBadRequest());
  }


  @Test
  void initializeStepUpSession_givenLessThanExpiredIn_thenReturn400() throws Exception {
    InitStepUpSessionDto request = InitStepUpSessionDto.builder()
        .authFactorRules(List.of(AuthFactorRule.builder().factor(AuthFactor.OTP).build()))
        .flowId(UUID.randomUUID().toString())
        .flowName("NAME").identifierId(UUID.randomUUID().toString())
        .identifierType(IdentifierType.PROFILE_ID).authConfig(validOTPFactorConfigs).expiredIn(10)
        .build();
    mockMvc.perform(post("/internal/step-up/init").contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON).content(objectMapper.writeValueAsString(request)))
        .andExpect(status().isBadRequest());
  }

  @Test
  void initializeStepUpSession_givenExceedExpiredIn_thenReturn400() throws Exception {
    InitStepUpSessionDto request = InitStepUpSessionDto.builder()
        .authFactorRules(List.of(AuthFactorRule.builder().factor(AuthFactor.OTP).build()))
        .flowId(UUID.randomUUID().toString())
        .flowName("NAME").identifierId(UUID.randomUUID().toString())
        .identifierType(IdentifierType.PROFILE_ID).expiredIn(1200).authConfig(validOTPFactorConfigs)
        .build();
    mockMvc.perform(post("/internal/step-up/init").contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON).content(objectMapper.writeValueAsString(request)))
        .andExpect(status().isBadRequest());
  }

  @Test
  @SneakyThrows
  void giveValidStepUpSessionUpdateRequest_thenShouldSuccessfullyUpdates() {
    var request = StepUpUpdateRequest.builder().stepUpAuthId("sua-1")
        .authFactor(AuthFactor.DEVICE_BIO).isKeepCurrentFactor(false).attempts(1).maxAttempts(1)
        .passed(true).build();
    mockMvc.perform(put("/internal/step-up")
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(request)))
        .andExpect(status().is2xxSuccessful());
  }

  @Test
  @SneakyThrows
  void giveValidStepUpVerificationRequest_thenShouldSuccessfullyVerification() {

    StepUpVerificationRequest request = StepUpVerificationRequest.builder()
        .stepUpAuthId("step_up_id")
        .flowId("flow_id")
        .isIncludeHistoryTrail(true)
        .build();
    when(stepUpService.verifyStepUpSession(any())).thenReturn(StepUpVerificationResponse.builder()
        .result(FactorStatus.SUCCESS.name())
        .build());
    var response = mockMvc.perform(post("/internal/step-up/verification")
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(request)))
        .andExpect(status().is2xxSuccessful())
        .andReturn()
        .getResponse();

    Assertions.assertEquals(HttpStatus.OK.value(), response.getStatus());
  }

  @Test
  void shouldReturn200_WhenValidateToken_AndSuccess() throws Exception {
    var request = TestHelper.buildStepUpValidationRequest(AuthFactor.DEVICE_BIO, false);
    mockMvc.perform(MockMvcRequestBuilders
        .post("/internal/step-up/validation")
        .contentType("application/json")
        .content(objectMapper.writeValueAsString(request))
    ).andExpect(status().is2xxSuccessful());
    verify(stepUpService, times(1)).validateStepUpSession(request);

  }

  @Test
  void initializeStepUpSession_givenValidRequest_thenReturn201() throws Exception {
    InitStepUpSessionDto request = InitStepUpSessionDto.builder()
        .authFactorRules(List.of(AuthFactorRule.builder().factor(AuthFactor.OTP).build()))
        .flowId(UUID.randomUUID().toString())
        .flowName("NAME")
        .identifierId(UUID.randomUUID().toString())
        .identifierType(IdentifierType.PROFILE_ID)
        .authConfig(validOTPFactorConfigs)
        .appId("appId")
        .build();
    when(stepUpService.initStepUpSession(any())).thenReturn(
        StepUpSessionResponse.builder().stepUpAuthId("authId").build());
    var response = mockMvc.perform(post("/internal/step-up/init")
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(request)))
        .andExpect(status().isCreated())
        .andReturn()
        .getResponse();

    Assertions.assertEquals(HttpStatus.CREATED.value(), response.getStatus());
  }

  @Test
  void initializeStepUpSession_givenMissingAppId_thenReturn400() throws Exception {
    InitStepUpSessionDto request = InitStepUpSessionDto.builder()
        .authFactorRules(List.of(AuthFactorRule.builder().factor(AuthFactor.OTP).build()))
        .flowId(UUID.randomUUID().toString())
        .flowName("NAME")
        .identifierId(UUID.randomUUID().toString())
        .identifierType(IdentifierType.PROFILE_ID)
        .authConfig(validOTPFactorConfigs)
        .build();
    var response = mockMvc.perform(post("/internal/step-up/init")
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(request)))
        .andExpect(status().is4xxClientError())
        .andReturn()
        .getResponse();

    Assertions.assertEquals(HttpStatus.BAD_REQUEST.value(), response.getStatus());
  }


  @Test
  void initializeStepUpSession_givenInvalidRequest_thenReturn400() throws Exception {
    InitStepUpSessionDto request = InitStepUpSessionDto.builder()
        .authFactorRules(List.of(AuthFactorRule.builder().factor(AuthFactor.OTP).build()))
        .flowName("NAME")
        .identifierId(UUID.randomUUID().toString())
        .authConfig(validOTPFactorConfigs)
        .build();
    var response = mockMvc.perform(post("/internal/step-up/init")
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(request)))
        .andExpect(status().is4xxClientError())
        .andReturn()
        .getResponse();

    Assertions.assertEquals(HttpStatus.BAD_REQUEST.value(), response.getStatus());
  }

  @Test
  void getHistoryTrail_givenValidRequest_thenReturn200() throws Exception {
    var response = mockMvc.perform(get("/internal/step-up/history-trail")
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON)
            .param("stepUpAuthId", UUID.randomUUID().toString())
        ).andExpect(status().is2xxSuccessful())
        .andReturn()
        .getResponse();

    Assertions.assertEquals(HttpStatus.OK.value(), response.getStatus());
  }

  @Test
  void getHistoryTrail_givenInvalidRequest_thenReturn400() throws Exception {
    var response = mockMvc.perform(get("/internal/step-up/history-trail")
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON)
            .param("stepUpAuthId", "")
        ).andExpect(status().is4xxClientError())
        .andReturn()
        .getResponse();

    Assertions.assertEquals(HttpStatus.BAD_REQUEST.value(), response.getStatus());
  }

  @SneakyThrows
  @ParameterizedTest
  @MethodSource("provideInvalidProfile")
  void shouldThrowException_whenNoProfileIdForDeviceBio(String profileId) {
    InitStepUpSessionDto request = InitStepUpSessionDto.builder()
        .authFactorRules(List.of(AuthFactorRule.builder().factor(AuthFactor.DEVICE_BIO).build()))
        .flowId(UUID.randomUUID().toString())
        .flowName("NAME").identifierId(profileId).identifierType(IdentifierType.PROFILE_ID)
        .authConfig(Map.of(AuthFactor.DEVICE_BIO,
            DeviceBioAuthConfig.builder().username("username").deviceId("deviceId").build()))
        .build();
    var response = mockMvc.perform(
            post("/internal/step-up/init").contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON).content(objectMapper.writeValueAsString(request)))
        .andExpect(status().isBadRequest()).andReturn().getResponse();

    Assertions.assertEquals(HttpStatus.BAD_REQUEST.value(), response.getStatus());
    Assertions.assertTrue(response.getContentAsString()
        .contains(ErrorCode.INVALID_REQUIRED_FIELDS.toUniversalCode()));
  }

  @SneakyThrows
  @ParameterizedTest
  @MethodSource("provideInvalidAuthConfigForDeviceBio")
  void shouldThrowException_whenInvalidAuthConfigForDeviceBio(Map<AuthFactor, Object> authConfig) {
    InitStepUpSessionDto request = InitStepUpSessionDto.builder()
        .authFactorRules(List.of(AuthFactorRule.builder().factor(AuthFactor.DEVICE_BIO).build()))
        .flowId(UUID.randomUUID().toString())
        .flowName("NAME").identifierId(UUID.randomUUID().toString())
        .identifierType(IdentifierType.PROFILE_ID).authConfig(authConfig).build();
    var response = mockMvc.perform(
            post("/internal/step-up/init").contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON).content(objectMapper.writeValueAsString(request)))
        .andExpect(status().isBadRequest()).andReturn().getResponse();

    Assertions.assertEquals(HttpStatus.BAD_REQUEST.value(), response.getStatus());
    Assertions.assertTrue(response.getContentAsString()
        .contains(ErrorCode.INVALID_REQUIRED_FIELDS.toUniversalCode()));
    Assertions.assertTrue(
        response.getContentAsString().contains("authConfig") || response.getContentAsString()
            .contains("authConfig.DEVICE_BIO") || response.getContentAsString()
            .contains("authConfig.DEVICE_BIO.deviceId"));
  }

  @Test
  @SneakyThrows
  void shouldThrowException_whenInvalidIdentifierTypeForDeviceBio() {
    InitStepUpSessionDto request = InitStepUpSessionDto.builder()
        .authFactorRules(List.of(AuthFactorRule.builder().factor(AuthFactor.DEVICE_BIO).build()))
        .flowId(UUID.randomUUID().toString())
        .flowName("NAME").identifierId(UUID.randomUUID().toString())
        .identifierType(IdentifierType.SESSION)
        .authConfig(Map.of(AuthFactor.DEVICE_BIO,
            DeviceBioAuthConfig.builder().username("username").deviceId("deviceId").build()))
        .build();
    var response = mockMvc.perform(
            post("/internal/step-up/init").contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON).content(objectMapper.writeValueAsString(request)))
        .andExpect(status().isBadRequest()).andReturn().getResponse();

    Assertions.assertEquals(HttpStatus.BAD_REQUEST.value(), response.getStatus());
    Assertions.assertTrue(response.getContentAsString()
        .contains(ErrorCode.INVALID_REQUIRED_FIELDS.toUniversalCode()));
    Assertions.assertTrue(response.getContentAsString().contains("identifierType"));
  }

  @Test
  @SneakyThrows
  void shouldSuccess_whenValidIdentifierTypeForDeviceBio() {
    InitStepUpSessionDto request = InitStepUpSessionDto.builder()
        .authFactorRules(List.of(AuthFactorRule.builder().factor(AuthFactor.DEVICE_BIO).build()))
        .flowId(UUID.randomUUID().toString())
        .flowName("NAME").identifierId(UUID.randomUUID().toString())
        .identifierType(IdentifierType.PROFILE_ID)
        .appId("appId")
        .authConfig(Map.of(AuthFactor.DEVICE_BIO,
            DeviceBioAuthConfig.builder().username("username").deviceId("deviceId").build()))
        .build();
    when(stepUpService.initStepUpSession(any())).thenReturn(
        StepUpSessionResponse.builder().stepUpAuthId("authId").build());
    mockMvc.perform(post("/internal/step-up/init").contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON).content(objectMapper.writeValueAsString(request)))
        .andExpect(status().isCreated());
  }

  @Test
  @SneakyThrows
  void shouldThrowException_whenInvalidIdentifierTypeForPasscode() {
    InitStepUpSessionDto request = InitStepUpSessionDto.builder()
        .authFactorRules(List.of(AuthFactorRule.builder().factor(AuthFactor.PASSCODE).build()))
        .flowId(UUID.randomUUID().toString())
        .flowName("NAME").identifierId(UUID.randomUUID().toString())
        .identifierType(IdentifierType.SESSION)
        .authConfig(
            Map.of(AuthFactor.PASSCODE, PasscodeAuthConfig.builder().username("username").build()))
        .build();
    var response = mockMvc.perform(
            post("/internal/step-up/init").contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON).content(objectMapper.writeValueAsString(request)))
        .andExpect(status().isBadRequest()).andReturn().getResponse();

    Assertions.assertEquals(HttpStatus.BAD_REQUEST.value(), response.getStatus());
    Assertions.assertTrue(response.getContentAsString()
        .contains(ErrorCode.INVALID_REQUIRED_FIELDS.toUniversalCode()));
    Assertions.assertTrue(response.getContentAsString().contains("identifierType"));
  }

  @Test
  @SneakyThrows
  void shouldSuccess_whenValidIdentifierTypeForPasscode() {
    InitStepUpSessionDto request = InitStepUpSessionDto.builder()
        .authFactorRules(List.of(AuthFactorRule.builder().factor(AuthFactor.PASSCODE).build()))
        .flowId(UUID.randomUUID().toString())
        .flowName("NAME").identifierId(UUID.randomUUID().toString())
        .identifierType(IdentifierType.PROFILE_ID)
        .appId("appId")
        .authConfig(
            Map.of(AuthFactor.PASSCODE, PasscodeAuthConfig.builder().username("username").build()))
        .build();
    when(stepUpService.initStepUpSession(any())).thenReturn(
        StepUpSessionResponse.builder().stepUpAuthId("authId").build());

    mockMvc.perform(post("/internal/step-up/init").contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON).content(objectMapper.writeValueAsString(request)))
        .andExpect(status().isCreated());
  }

  private static Stream<Arguments> provideInvalidProfile() {
    return Stream.of(Arguments.of(""), Arguments.of((String) null),
        Arguments.of("                 "));
  }

  private static Stream<Arguments> provideInvalidAuthConfigForDeviceBio() {
    Map<AuthFactor, Object> authConfig = new EnumMap<>(AuthFactor.class);
    var deviceBioAuthConfig = DeviceBioAuthConfig.builder().deviceId(null).username("1234").build();
    authConfig.put(AuthFactor.DEVICE_BIO, deviceBioAuthConfig);
    Map<AuthFactor, Object> authConfig2 = new EnumMap<>(AuthFactor.class);
    Map<AuthFactor, Object> authConfig3 = new EnumMap<>(AuthFactor.class);
    Map<AuthFactor, Object> authConfig4 = new EnumMap<>(AuthFactor.class);
    var deviceBioAuthConfig1 = DeviceBioAuthConfig.builder().deviceId("valid-id").username("1234")
        .linkDeviceDays(-1L).build();
    var deviceBioAuthConfig2 = DeviceBioAuthConfig.builder().deviceId("valid-id").username("1234")
        .deviceBioEnrollDays(-10L).build();
    authConfig3.put(AuthFactor.DEVICE_BIO, deviceBioAuthConfig1);
    authConfig4.put(AuthFactor.DEVICE_BIO, deviceBioAuthConfig2);
    return Stream.of(Arguments.of(new HashMap<>()), Arguments.of((Map<AuthFactor, Object>) null),
        Arguments.of(authConfig), Arguments.of(authConfig2), Arguments.of(authConfig3),
        Arguments.of(authConfig4));
  }

}