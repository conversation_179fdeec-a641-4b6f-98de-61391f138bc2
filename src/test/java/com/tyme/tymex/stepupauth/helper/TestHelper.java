package com.tyme.tymex.stepupauth.helper;


import com.tyme.tymex.stepupauth.controller.domain.InitStepUpSessionDto;
import com.tyme.tymex.stepupauth.controller.domain.StepUpUpdateRequest;
import com.tyme.tymex.stepupauth.controller.domain.StepUpValidationRequest;
import com.tyme.tymex.stepupauth.controller.domain.StepUpVerificationRequest;
import com.tyme.tymex.stepupauth.domain.AuthFactor;
import com.tyme.tymex.stepupauth.domain.DeviceBioAuthConfig;
import com.tyme.tymex.stepupauth.domain.DeviceBioEnrollStatus;
import com.tyme.tymex.stepupauth.domain.IdentifierType;
import com.tyme.tymex.stepupauth.domain.ProfileInfo;
import com.tyme.tymex.stepupauth.domain.StepUpStatus;
import com.tyme.tymex.stepupauth.infra.connector.model.DeviceIdType;
import com.tyme.tymex.stepupauth.infra.connector.model.profile.ProfilePhoneData;
import com.tyme.tymex.stepupauth.infra.dynamodb.domain.LinkDeviceInfo;
import com.tyme.tymex.stepupauth.infra.dynamodb.domain.StepUpAuthDeviceBioEnrollment;
import com.tyme.tymex.stepupauth.infra.dynamodb.domain.StepUpAuthFactor;
import com.tyme.tymex.stepupauth.infra.dynamodb.domain.StepUpEntity;
import com.tyme.tymex.stepupauth.infra.event.DeviceBioEnrollmentDetail;
import com.tyme.tymex.stepupauth.infra.event.Event;
import com.tyme.tymex.stepupauth.infra.event.EventType;
import com.tyme.tymex.stepupauth.utils.GlobalUtils;
import java.time.Instant;
import java.util.EnumMap;
import java.util.Map;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class TestHelper {

  public static StepUpUpdateRequest buildStepUpUpdateRequest(String authId) {
    return StepUpUpdateRequest.builder().stepUpAuthId(authId).authFactor(AuthFactor.OTP)
        .passed(true).attempts(2).maxAttempts(2).isKeepCurrentFactor(false).build();
  }

  public static StepUpVerificationRequest buildStepUpVerificationRequest(
      String flowId,
      String stepUpAuthId, boolean isIncludeHistoryTrail) {
    return StepUpVerificationRequest
        .builder()
        .flowId(flowId)
        .stepUpAuthId(stepUpAuthId)
        .isIncludeHistoryTrail(isIncludeHistoryTrail)
        .build();

  }

  public static StepUpValidationRequest buildStepUpValidationRequest(
      AuthFactor factor, Boolean includeFactorConfig) {

    return StepUpValidationRequest
        .builder()
        .stepUpAuthId("abc123")
        .authFactor(factor)
        .includeFactorConfig(includeFactorConfig)
        .build();

  }

  public static InitStepUpSessionDto buildStepUpSessionDto() {
    return InitStepUpSessionDto.builder()
        .expiredIn(600)
        .identifierId("123456")
        .flowName("EFT")
        .appId("appId")
        .build();
  }

  public static ProfileInfo buildProfileInfo() {
    return ProfileInfo.builder()
        .id("testId")
        .personPhoneData(ProfilePhoneData
            .builder()
            .dialCode("+63")
            .phoneNumber("6123456")
            .build())
        .build();
  }

  public static Map<AuthFactor, Object> buildAuthConfigWithDeviceBio() {
    Map<AuthFactor, Object> authConfig = new EnumMap<>(AuthFactor.class);
    var deviceBioAuthConfig = DeviceBioAuthConfig.builder()
        .deviceId("abc123")
        .internalDeviceId("abc123")
        .username("1234")
        .build();
    authConfig.put(AuthFactor.DEVICE_BIO, deviceBioAuthConfig);

    return authConfig;

  }


  public static StepUpEntity buildStepUpSessionEntity(String authId) {
    return buildStepUpEntity(GlobalUtils.toStepUpSessionPk(authId),
        GlobalUtils.toStepUpSessionSk(authId), authId, AuthFactor.OTP);
  }

  public static StepUpEntity buildStepUpFactorEntity(String authId, AuthFactor factor) {
    return buildStepUpEntity(GlobalUtils.toStepUpSessionPk(authId), GlobalUtils.toFactorSk(factor),
        authId, factor);
  }

  public static StepUpEntity buildStepUpEntity(String pk, String sk, String authId,
      AuthFactor factor) {
    return StepUpEntity.builder()
        .pk(pk)
        .sk(sk)
        .identifierId("profile_id")
        .identifierType(IdentifierType.PROFILE_ID)
        .stepUpAuthId(authId)
        .factor(factor)
        .currentFactor(factor)
        .currentFactorId(1)
        .expiredTime(Instant.now().plusSeconds(200))
        .overallStatus(StepUpStatus.IN_PROGRESS)
        .flowId("flow_id")
        .createdDate(Instant.now())
        .modifiedDate(Instant.now())
        .authFactorRules(Map.of(1,
            StepUpAuthFactor.builder().factor(AuthFactor.OTP).index(1).parentIndex(1).build()))
        .factorConfigs(Map.of(AuthFactor.OTP, "dummy"))
        .currentFactorId(1)
        .attempt(1)
        .build();
  }

  public static Event<DeviceBioEnrollmentDetail> createDeviceBioEnrollmentEvent() {
    var deviceBioEnrollmentDetail = DeviceBioEnrollmentDetail.builder()
        .profileId("testProfileId")
        .deviceId("testDeviceId")
        .channel("IOS")
        .biometricType("Facial")
        .flowName("test")
        .status("enroll")
        .securityLevel("1")
        .createdTime(Instant.now().toEpochMilli())
        .build();

    return Event.<DeviceBioEnrollmentDetail>builder()
        .time(Instant.now().toEpochMilli())
        .type(EventType.DEVICE_BIO_ENROLLMENT.name())
        .detail(deviceBioEnrollmentDetail)
        .build();
  }

  public static StepUpAuthDeviceBioEnrollment buildStepUpAuthDeviceBioEnrollment(
      DeviceBioEnrollStatus status) {
    return StepUpAuthDeviceBioEnrollment.builder()
        .pk("testPk")
        .sk("testSk")
        .deviceId("testDeviceId")
        .channel("iOS")
        .biometricType("Facial")
        .flowName("test")
        .status(status.name())
        .securityLevel("1")
        .eventTime(Instant.now().toEpochMilli())
        .build();
  }

  public static LinkDeviceInfo buildLinkDeviceInfo(String deviceId, Long linkedAt,
      DeviceIdType deviceIdType) {
    return LinkDeviceInfo.builder()
        .partitionKey("testPk")
        .sk("testSk")
        .deviceId(deviceId)
        .linkedAt(linkedAt)
        .deviceIdType(deviceIdType)
        .build();
  }

}