package com.tyme.tymex.stepupauth.cucumber;

import com.tyme.tymex.stepupauth.StepUpAuthApplicationTestsBase;
import com.tyme.tymex.stepupauth.controller.domain.StepUpUpdateRequest;
import com.tyme.tymex.stepupauth.domain.AuthFactor;
import com.tyme.tymex.stepupauth.domain.FactorStatus;
import com.tyme.tymex.stepupauth.domain.FactorType;
import com.tyme.tymex.stepupauth.domain.IdentifierType;
import com.tyme.tymex.stepupauth.domain.StepUpStatus;
import com.tyme.tymex.stepupauth.infra.dynamodb.domain.HistoryTrailDetail;
import com.tyme.tymex.stepupauth.infra.dynamodb.domain.StepUpAuthFactor;
import com.tyme.tymex.stepupauth.infra.dynamodb.domain.StepUpEntity;
import com.tyme.tymex.stepupauth.infra.exception.DomainException;
import com.tyme.tymex.stepupauth.infra.exception.model.ErrorCode;
import com.tyme.tymex.stepupauth.repository.StepUpRepo;
import com.tyme.tymex.stepupauth.service.StepUpService;
import com.tyme.tymex.stepupauth.utils.GlobalUtils;
import io.cucumber.java.Before;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.log4j.Log4j2;
import org.junit.jupiter.api.Assertions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@Log4j2
@SpringBootTest
public class SessionUpdatesAPICucumberSteps extends StepUpAuthApplicationTestsBase {

  @Autowired
  StepUpService stepUpService;
  @Autowired
  StepUpRepo stepUpRepo;

  String authId;
  AuthFactor factorToUpdated;
  boolean isKeepCurrentFactor;
  int maxAttempts;
  int attempts;
  boolean factorPassed;
  String factorErrorCode;
  StepUpUpdateRequest request;
  Exception givenException;

  @Before
  public void init() {
    givenException = null;
    factorErrorCode = null;
    authId = "INITIALIZED_" + UUID.randomUUID();
    factorToUpdated = AuthFactor.DEVICE_BIO;
    isKeepCurrentFactor = false;
    maxAttempts = 1;
    attempts = 1;
    factorPassed = true;
    request = StepUpUpdateRequest.builder().stepUpAuthId(authId).authFactor(factorToUpdated).isKeepCurrentFactor(isKeepCurrentFactor).maxAttempts(maxAttempts).attempts(attempts).passed(factorPassed)
        .build();
  }

  @Given("A Step-Up Session is not presented in our database")
  public void aStepUpSessionIsNotPresentedInOurDatabase() {
    log.info("");
  }

  @When("The factor init a new request with this session with passed result")
  public void theFactorInitANewRequestWithThisSession() {
    doPassedRequest();
  }

  @Then("Service should return a bad request with a specific error code")
  public void serviceShouldReturnABadRequestWithASpecificErrorCode() {
    assertDomainExceptionEquals(givenException, ErrorCode.STEP_UP_SESSION_NOT_EXIST);
  }

  @Given("A Step-Up Session is presented but have not a Factor record yet in our database")
  public void aStepUpSessionIsPresentedButHaveNotAFactorRecordYetInOurDatabase() {
    authId = "NO_FACTOR_RECORD_" + UUID.randomUUID();
    var sessionEntity = buildSessionEntity(authId);
    sessionEntity.setCreatedDate(Instant.now());
    stepUpRepo.saveStepUp(sessionEntity);

    var sessionOrFactorEntities = stepUpRepo.queryByAuthId(authId).toList();
    Assertions.assertNotNull(sessionOrFactorEntities);
    Assertions.assertEquals(1, sessionOrFactorEntities.size());
    Assertions.assertEquals(GlobalUtils.toStepUpSessionSk(authId), sessionOrFactorEntities.getFirst().getSk());

    factorToUpdated = sessionEntity.getCurrentFactor();
  }

  @Then("Service should insert a new Factor record and update overall status of the Step-Up Session record")
  public void serviceShouldInsertANewFactorRecordAndUpdateOverallStatusOfTheStepUpSessionRecord() {
    Assertions.assertNull(givenException);
    var sessionOrFactorMap = getStepUpEntityMap();
    assertMapSizeEquals(sessionOrFactorMap, 2);

    var sessionEntity = sessionOrFactorMap.get(GlobalUtils.toStepUpSessionSk(authId));
    var factorEntity = sessionOrFactorMap.get(GlobalUtils.toFactorSk(factorToUpdated));
    assertSessionEntityUpdatedSucceed(sessionEntity, authId);
    assertPrimaryFactorEntityStatus(factorEntity, FactorStatus.SUCCESS, AuthFactor.DEVICE_BIO, 1, attempts, maxAttempts, 1);

    Assertions.assertEquals(factorEntity.getModifiedDate().getEpochSecond(), factorEntity.getCreatedDate().getEpochSecond());

    assertFirstFactorHistorySucceed(factorEntity, attempts, maxAttempts);
    factorRecordShouldNotHaveUnexpectedFields(factorEntity);
  }

  @Given("A Step-Up Session is presented and a Factor record updated first time in our database")
  public void aStepUpSessionIsPresentedAndAFactorRecordUpdatedFirstTimeInOurDatabase() {
    authId = "FIRST_TIME_FACTOR_RECORD_" + UUID.randomUUID();
    var sessionEntity = buildSessionEntity(authId);
    var factorEntity = buildFactorEntity(authId, AuthFactor.DEVICE_BIO, FactorStatus.FAILED);
    stepUpRepo.transactUpsertWithRollback(sessionEntity, factorEntity);

    var sessionOrFactorMap = getStepUpEntityMap();
    assertMapSizeEquals(sessionOrFactorMap, 2);
    factorEntity = sessionOrFactorMap.get(GlobalUtils.toFactorSk(factorToUpdated));
    Assertions.assertEquals(FactorStatus.FAILED, factorEntity.getFactorResult());

    factorToUpdated = sessionEntity.getCurrentFactor();
    attempts = 2;
    maxAttempts = factorEntity.getMaxAttempts();
  }

  @Then("Service should update the Factor record second time and update the Step-Up Session record")
  public void serviceShouldUpdateTheFactorRecordSecondTimeAndUpdateTheStepUpSessionRecord() {
    Assertions.assertNull(givenException);
    var sessionOrFactorMap = getStepUpEntityMap();
    assertMapSizeEquals(sessionOrFactorMap, 2);
    var sessionEntity = sessionOrFactorMap.get(GlobalUtils.toStepUpSessionSk(authId));
    var factorEntity = sessionOrFactorMap.get(GlobalUtils.toFactorSk(factorToUpdated));

    assertSessionEntityUpdatedSucceed(sessionEntity, authId);
    assertPrimaryFactorEntityStatus(factorEntity, FactorStatus.SUCCESS, factorToUpdated, 1, attempts, maxAttempts, 2);
    factorRecordShouldNotHaveUnexpectedFields(factorEntity);
    assertFirstFactorHistoryFailed(factorEntity, 1, maxAttempts);
    assertLastFactorHistorySucceed(factorEntity, attempts, maxAttempts);
    Assertions.assertTrue(factorEntity.getModifiedDate().isAfter(factorEntity.getCreatedDate()));
    Assertions.assertEquals(factorEntity.getModifiedDate().getEpochSecond(), factorEntity.getFactorHistory().getLast().getTimestamp().getEpochSecond());

  }

  @Given("A presented Step-Up Session have fallback rules and a Factor record updated first time in our database")
  public void aPresentedStepUpSessionHaveFallbackRulesAndAFactorRecordUpdatedFirstTimeInOurDatabase() {
    authId = "FIRST_TIME_FACTOR_RECORD_" + UUID.randomUUID();
    var sessionEntity = buildSessionEntity(authId);
    var factorEntity = buildFactorEntity(authId, AuthFactor.DEVICE_BIO, FactorStatus.FAILED);
    stepUpRepo.transactUpsertWithRollback(sessionEntity, factorEntity);

    var sessionOrFactorMap = getStepUpEntityMap();
    assertMapSizeEquals(sessionOrFactorMap, 2);
    factorEntity = sessionOrFactorMap.get(GlobalUtils.toFactorSk(AuthFactor.DEVICE_BIO));
    assertPrimaryFactorEntityStatus(factorEntity, FactorStatus.FAILED, AuthFactor.DEVICE_BIO, 1, 1, 5, 1);
    assertFirstFactorHistoryFailed(factorEntity, 1, 5);

    factorToUpdated = sessionEntity.getCurrentFactor();
    attempts = 2;
    maxAttempts = factorEntity.getMaxAttempts();
    factorErrorCode = ErrorCode.UNKNOWN_ERROR.toUniversalCode();
  }

  @When("The factor init a new request with this session with failed result")
  public void theFactorInitANewRequestWithThisSessionWithFailedResult() {
    doFailedRequest();
  }

  @Then("Service should update the Factor and Session records with a correct next factor")
  public void serviceShouldUpdateTheFactorAndSessionRecordsWithACorrectNextFactor() {
    Assertions.assertNull(givenException);
    var sessionOrFactorMap = getStepUpEntityMap();
    assertMapSizeEquals(sessionOrFactorMap, 2);
    var sessionEntity = sessionOrFactorMap.get(GlobalUtils.toStepUpSessionSk(authId));
    var factorEntity = sessionOrFactorMap.get(GlobalUtils.toFactorSk(factorToUpdated));

    assertSessionEntityUpdatedInProgress(sessionEntity, authId, AuthFactor.OTP, 2);
    assertPrimaryFactorEntityStatus(factorEntity, FactorStatus.FAILED, factorToUpdated, 1, attempts, maxAttempts, 2);
    factorRecordShouldNotHaveUnexpectedFields(factorEntity);
    assertFirstFactorHistoryFailed(factorEntity, 1, maxAttempts);
    assertLastFactorHistoryFailed(factorEntity, attempts, maxAttempts);
    Assertions.assertTrue(factorEntity.getModifiedDate().isAfter(factorEntity.getCreatedDate()));
    Assertions.assertEquals(factorEntity.getModifiedDate().getEpochSecond(), factorEntity.getFactorHistory().getLast().getTimestamp().getEpochSecond());
  }

  @Given("A presented Step-Up Session have fallback rules and a primary factor record completed in our database")
  public void aPresentedStepUpSessionHaveFallbackRulesAndAPrimaryFactorRecordCompletedInOurDatabase() {
    factorToUpdated = AuthFactor.OTP;
    authId = "COMPLETED_PRIMARY_FACTOR_RECORD_" + UUID.randomUUID();
    var sessionEntity = buildSessionEntity(authId);
    sessionEntity.setCurrentFactor(factorToUpdated);
    sessionEntity.setCurrentFactorId(2);
    var deviceBioEntity = buildFactorEntity(authId, AuthFactor.DEVICE_BIO, FactorStatus.FAILED);
    stepUpRepo.transactUpsertWithRollback(sessionEntity, deviceBioEntity);

    var sessionOrFactorMap = getStepUpEntityMap();
    assertMapSizeEquals(sessionOrFactorMap, 2);
    deviceBioEntity = sessionOrFactorMap.get(GlobalUtils.toFactorSk(AuthFactor.DEVICE_BIO));
    assertPrimaryFactorEntityStatus(deviceBioEntity, FactorStatus.FAILED, AuthFactor.DEVICE_BIO, 1, 1, 5, 1);
    assertFirstFactorHistoryFailed(deviceBioEntity, 1, 5);

    maxAttempts = deviceBioEntity.getMaxAttempts();
  }

  @Then("Service should update the fallback Factor and Session records successfully")
  public void serviceShouldUpdateTheFallbackFactorAndSessionRecordsSuccessfully() {
    Assertions.assertNull(givenException);
    var sessionOrFactorMap = getStepUpEntityMap();
    assertMapSizeEquals(sessionOrFactorMap, 3);
    var sessionEntity = sessionOrFactorMap.get(GlobalUtils.toStepUpSessionSk(authId));
    assertSessionEntityUpdatedSucceed(sessionEntity, authId);

    var otpFactorEntity = sessionOrFactorMap.get(GlobalUtils.toFactorSk(factorToUpdated));
    assertFallbackFactorEntityStatus(otpFactorEntity, FactorStatus.SUCCESS, AuthFactor.OTP, 2, attempts, maxAttempts, 1);
    assertFirstFactorHistorySucceed(otpFactorEntity, attempts, maxAttempts);
    factorRecordShouldNotHaveUnexpectedFields(otpFactorEntity);
    Assertions.assertEquals(otpFactorEntity.getModifiedDate().getEpochSecond(), otpFactorEntity.getCreatedDate().getEpochSecond());

    var deviceBioFactorEntity = sessionOrFactorMap.get(GlobalUtils.toFactorSk(AuthFactor.DEVICE_BIO));
    assertPrimaryFactorEntityStatus(deviceBioFactorEntity, FactorStatus.FAILED, AuthFactor.DEVICE_BIO, 1, 1, maxAttempts, 1);
    assertFirstFactorHistoryFailed(deviceBioFactorEntity, 1, maxAttempts);
    factorRecordShouldNotHaveUnexpectedFields(deviceBioFactorEntity);
    Assertions.assertEquals(deviceBioFactorEntity.getModifiedDate().getEpochSecond(), deviceBioFactorEntity.getCreatedDate().getEpochSecond());
    Assertions.assertEquals(deviceBioFactorEntity.getModifiedDate().getEpochSecond(), deviceBioFactorEntity.getFactorHistory().getFirst().getTimestamp().getEpochSecond());
  }

  @Given("A factor record presented in database with a specific maxAttempt value")
  public void aFactorRecordPresentedInDatabaseWithASpecificMaxAttemptValue() {
    authId = "MAX_ATTEMPT_VALIDATION_FACTOR_RECORD_" + UUID.randomUUID();
    var sessionEntity = buildSessionEntity(authId);
    var deviceBioEntity = buildFactorEntity(authId, AuthFactor.DEVICE_BIO, FactorStatus.FAILED);
    stepUpRepo.saveStepUp(sessionEntity);
    stepUpRepo.saveStepUp(deviceBioEntity);

    factorToUpdated = sessionEntity.getCurrentFactor();
    attempts = 2;
    maxAttempts = 2;
  }

  @Then("Service should throw an exception indicating that the maximum number of attempts has not been expected")
  public void serviceShouldThrowAnExceptionIndicatingThatTheMaximumNumberOfAttemptsHasNotBeenExpected() {
    assertDomainExceptionEquals(givenException, ErrorCode.AUTH_FACTOR_PROVIDED_INVALID);

    var sessionOrFactorMap = getStepUpEntityMap();
    assertMapSizeEquals(sessionOrFactorMap, 2);
    var sessionEntity = sessionOrFactorMap.get(GlobalUtils.toStepUpSessionSk(authId));
    assertSessionEntityNotUpdated(sessionEntity, authId, StepUpStatus.IN_PROGRESS, factorToUpdated, 1);

    var deviceBioFactorEntity = sessionOrFactorMap.get(GlobalUtils.toFactorSk(AuthFactor.DEVICE_BIO));
    assertPrimaryFactorEntityStatus(deviceBioFactorEntity, FactorStatus.FAILED, AuthFactor.DEVICE_BIO, 1, 1, 5, 1);
    assertFirstFactorHistoryFailed(deviceBioFactorEntity, 1, deviceBioFactorEntity.getMaxAttempts());
    factorRecordShouldNotHaveUnexpectedFields(deviceBioFactorEntity);
    Assertions.assertEquals(deviceBioFactorEntity.getModifiedDate().getEpochSecond(), deviceBioFactorEntity.getCreatedDate().getEpochSecond());
    Assertions.assertEquals(deviceBioFactorEntity.getModifiedDate().getEpochSecond(), deviceBioFactorEntity.getFactorHistory().getFirst().getTimestamp().getEpochSecond());
  }

  private void assertLastFactorHistoryFailed(StepUpEntity factorEntity, int attempts, int maxAttempts) {
    var lastFactorHistory = factorEntity.getFactorHistory().getLast();
    Assertions.assertNotNull(lastFactorHistory.getErrorCode());
    Assertions.assertTrue(lastFactorHistory.getErrorCode().matches("^200\\d{4}$"));
    assertLastFactorHistoryStatus(factorEntity, FactorStatus.FAILED, attempts, maxAttempts);
  }

  private void assertLastFactorHistorySucceed(StepUpEntity factorEntity, int attempts, int maxAttempts) {
    Assertions.assertNull(factorEntity.getFactorHistory().getLast().getErrorCode());
    assertLastFactorHistoryStatus(factorEntity, FactorStatus.SUCCESS, attempts, maxAttempts);
  }

  private void assertLastFactorHistoryStatus(StepUpEntity factorEntity, FactorStatus expectedStatus, int attempts, int maxAttempts) {
    Assertions.assertEquals(expectedStatus, factorEntity.getFactorHistory().getLast().getResult());
    Assertions.assertEquals(attempts, factorEntity.getFactorHistory().getLast().getAttempt());
    Assertions.assertEquals(maxAttempts, factorEntity.getFactorHistory().getLast().getMaxAttempts());
    Assertions.assertTrue(factorEntity.getFactorHistory().getLast().getTimestamp().isAfter(factorEntity.getFactorHistory().getFirst().getTimestamp()));
    Assertions.assertEquals(factorEntity.getModifiedDate().getEpochSecond(), factorEntity.getFactorHistory().getLast().getTimestamp().getEpochSecond());
  }

  private void assertFallbackFactorEntityStatus(StepUpEntity factorEntity, FactorStatus expectedStatus, AuthFactor expectedFactor, int expectedFactorId, int expectedAttempts, int expectedMaxAttempts,
      int expectedHistorySize) {
    assertFactorEntityStatus(factorEntity, FactorType.FALLBACK, expectedStatus, expectedFactor, expectedFactorId, expectedAttempts, expectedMaxAttempts, expectedHistorySize);
  }

  private void assertPrimaryFactorEntityStatus(StepUpEntity factorEntity, FactorStatus expectedStatus, AuthFactor expectedFactor, int expectedFactorId, int expectedAttempts, int expectedMaxAttempts,
      int expectedHistorySize) {
    assertFactorEntityStatus(factorEntity, FactorType.PRIMARY, expectedStatus, expectedFactor, expectedFactorId, expectedAttempts, expectedMaxAttempts, expectedHistorySize);
  }

  private void assertFactorEntityStatus(StepUpEntity factorEntity, FactorType type, FactorStatus expectedStatus, AuthFactor expectedFactor, int expectedFactorId, int expectedAttempts,
      int expectedMaxAttempts,
      int expectedHistorySize) {
    Assertions.assertEquals(GlobalUtils.toFactorSk(expectedFactor), factorEntity.getSk());
    Assertions.assertEquals(expectedFactor, factorEntity.getFactor());
    Assertions.assertEquals(expectedFactorId, factorEntity.getFactorId());
    Assertions.assertEquals(type, factorEntity.getFactorType());
    Assertions.assertEquals(expectedStatus, factorEntity.getFactorResult());
    Assertions.assertEquals(expectedAttempts, factorEntity.getAttempt());
    Assertions.assertEquals(expectedMaxAttempts, factorEntity.getMaxAttempts());
    Assertions.assertNotNull(factorEntity.getFactorHistory());
    Assertions.assertEquals(expectedHistorySize, factorEntity.getFactorHistory().size());
  }

  private void assertFirstFactorHistorySucceed(StepUpEntity factorEntity, int expectedAttempts, int expectedMaxAttempts) {
    Assertions.assertNull(factorEntity.getFactorHistory().getFirst().getErrorCode());
    assertFirstFactorHistory(factorEntity, FactorStatus.SUCCESS, expectedAttempts, expectedMaxAttempts);
  }

  private void assertFirstFactorHistoryFailed(StepUpEntity factorEntity, int expectedAttempts, int expectedMaxAttempts) {
    var firstFactorHistory = factorEntity.getFactorHistory().getFirst();
    Assertions.assertNotNull(firstFactorHistory.getErrorCode());
    Assertions.assertTrue(firstFactorHistory.getErrorCode().matches("^200\\d{4}$"));
    assertFirstFactorHistory(factorEntity, FactorStatus.FAILED, expectedAttempts, expectedMaxAttempts);
  }

  private void assertFirstFactorHistory(StepUpEntity factorEntity, FactorStatus expectedStatus, int expectedAttempts, int expectedMaxAttempts) {
    var firstFactorHistory = factorEntity.getFactorHistory().getFirst();
    Assertions.assertEquals(expectedAttempts, firstFactorHistory.getAttempt());
    Assertions.assertEquals(expectedMaxAttempts, firstFactorHistory.getMaxAttempts());
    Assertions.assertEquals(expectedStatus, firstFactorHistory.getResult());
  }

  private <T, R> void assertMapSizeEquals(Map<T, R> m, int size) {
    Assertions.assertNotNull(m);
    Assertions.assertEquals(size, m.size());
  }

  private void assertDomainExceptionEquals(Exception givenException, ErrorCode expectedErrorCode) {
    Assertions.assertNotNull(givenException);
    Assertions.assertInstanceOf(DomainException.class, givenException);
    Assertions.assertEquals(expectedErrorCode, ((DomainException) givenException).getErrorCode());
  }

  private void assertSessionEntityUpdatedInProgress(StepUpEntity sessionEntity, String expectedAuthId, AuthFactor expectedCurrentFactor, int expectedCurrentFactorId) {
    assertSessionEntityStatus(sessionEntity, expectedAuthId, StepUpStatus.IN_PROGRESS);
    Assertions.assertNotNull(sessionEntity.getCurrentFactor());
    Assertions.assertEquals(expectedCurrentFactor, sessionEntity.getCurrentFactor());
    Assertions.assertNotNull(sessionEntity.getCurrentFactorId());
    Assertions.assertEquals(expectedCurrentFactorId, sessionEntity.getCurrentFactorId());
  }

  private void assertSessionEntityUpdatedSucceed(StepUpEntity sessionEntity, String expectedAuthId) {
    assertSessionEntityStatus(sessionEntity, expectedAuthId, StepUpStatus.FACTOR_SUCCESS);
    Assertions.assertNull(sessionEntity.getCurrentFactor());
    Assertions.assertNull(sessionEntity.getCurrentFactorId());
  }

  private void assertSessionEntityNotUpdated(StepUpEntity sessionEntity, String expectedAuthId, StepUpStatus expectedStatus, AuthFactor expectedCurrentFactor, int expectedCurrentFactorId) {
    Assertions.assertEquals(GlobalUtils.toStepUpSessionSk(expectedAuthId), sessionEntity.getSk());
    Assertions.assertEquals(expectedStatus, sessionEntity.getOverallStatus());
    Assertions.assertFalse(sessionEntity.getModifiedDate().isAfter(sessionEntity.getCreatedDate()));
    Assertions.assertEquals(expectedCurrentFactor, sessionEntity.getCurrentFactor());
    Assertions.assertEquals(expectedCurrentFactorId, sessionEntity.getCurrentFactorId());
  }

  private void assertSessionEntityStatus(StepUpEntity sessionEntity, String expectedAuthId, StepUpStatus expectedStatus) {
    Assertions.assertEquals(GlobalUtils.toStepUpSessionSk(expectedAuthId), sessionEntity.getSk());
    Assertions.assertEquals(expectedStatus, sessionEntity.getOverallStatus());
    Assertions.assertTrue(sessionEntity.getModifiedDate().isAfter(sessionEntity.getCreatedDate()));
  }

  private Map<String, StepUpEntity> getStepUpEntityMap() {
    return stepUpRepo.queryByAuthId(authId).collect(Collectors.toMap(StepUpEntity::getSk, Function.identity()));
  }

  private void doFailedRequest() {
    request.setPassed(false);
    doRequest();
  }

  private void doPassedRequest() {
    request.setPassed(true);
    doRequest();
  }

  private void doRequest() {
    request.setStepUpAuthId(authId);
    request.setAttempts(attempts);
    request.setMaxAttempts(maxAttempts);
    request.setAuthFactor(factorToUpdated);
    request.setErrorCode(factorErrorCode);
    try {
      stepUpService.updateStepUpSession(request);
    } catch (Exception ex) {
      givenException = ex;
    }
  }

  private void factorRecordShouldNotHaveUnexpectedFields(StepUpEntity entity) {
    Assertions.assertTrue(
        entity.getStepUpAuthId() == null && entity.getCurrentFactor() == null && entity.getCurrentFactorId() == null && entity.getAuthFactorRules() == null && entity.getFactorConfigs() == null
            && entity.getOverallStatus() == null && entity.getExpiredTime() == null && entity.getFlowId() == null && entity.getFlowName() == null && entity.getIdentifierId() == null
            && entity.getIdentifierType() == null && entity.getMaxVerify() == null && entity.getVerifyTimes() == null);
  }

  private StepUpEntity buildSessionEntity(String authId) {
    var factorRules = Map.of(1, StepUpAuthFactor.builder().factor(AuthFactor.DEVICE_BIO).index(1).parentIndex(1).build(), 2,
        StepUpAuthFactor.builder().factor(AuthFactor.OTP).index(2).parentIndex(1).build());
    Map<AuthFactor, Object> factorConfigs = Map.of(AuthFactor.DEVICE_BIO, "dummy", AuthFactor.OTP, "dummy");
    return buildSessionEntity(authId, "dummyIdentifierId", IdentifierType.PROFILE_ID, "dummyFlowId", factorRules, factorConfigs);
  }

  private StepUpEntity buildFactorEntity(String authId, AuthFactor factor, FactorStatus factorResult) {
    var defaultAttempts = 1;
    var defaultMaxAttempts = 5;
    var currentTime = Instant.now();
    var history = List.of(HistoryTrailDetail.builder().attempt(defaultAttempts).maxAttempts(defaultMaxAttempts).result(factorResult).timestamp(currentTime)
        .errorCode(FactorStatus.SUCCESS == factorResult ? null : ErrorCode.UNKNOWN_ERROR.toUniversalCode()).build());
    return buildFactorEntity(authId, factor, 1, FactorType.PRIMARY, defaultAttempts, defaultMaxAttempts, factorResult, history, currentTime);
  }

  private StepUpEntity buildFactorEntity(String authId, AuthFactor factor, Integer factorId, FactorType type, int attempts, int maxAttempts, FactorStatus factorResult,
      List<HistoryTrailDetail> history, Instant time) {
    return StepUpEntity.builder().pk(GlobalUtils.toStepUpSessionPk(authId)).sk(GlobalUtils.toFactorSk(factor)).factor(factor).factorId(factorId).factorType(type).attempt(attempts)
        .maxAttempts(maxAttempts).factorResult(factorResult).factorHistory(history).createdDate(time).modifiedDate(time).build();
  }

  private StepUpEntity buildSessionEntity(String authId, String identifierId, IdentifierType type, String flowId, Map<Integer, StepUpAuthFactor> factorRules, Map<AuthFactor, Object> factorConfigs) {
    var currentTime = Instant.now();
    return StepUpEntity.builder().pk(GlobalUtils.toStepUpSessionPk(authId)).sk(GlobalUtils.toStepUpSessionSk(authId)).stepUpAuthId(authId).identifierId(identifierId).identifierType(type)
        .currentFactor(factorRules.get(1).getFactor()).currentFactorId(factorRules.get(1).getIndex()).expiredTime(Instant.now().plusSeconds(1000)).overallStatus(StepUpStatus.IN_PROGRESS)
        .flowId(flowId).createdDate(currentTime).modifiedDate(currentTime).authFactorRules(factorRules).factorConfigs(factorConfigs).build();
  }
}
