package com.tyme.tymex.stepupauth.service.impl.enricher;

import static com.tyme.tymex.stepupauth.domain.AuthFactor.OTP;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.tyme.tymex.stepupauth.domain.AuthFactor;
import com.tyme.tymex.stepupauth.domain.OtpAuthConfig;
import com.tyme.tymex.stepupauth.helper.TestHelper;
import java.util.EnumMap;
import java.util.Map;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class OtpEnricherTest {

  @Spy
  private ObjectMapper objectMapper = new ObjectMapper();
  @InjectMocks
  private OtpEnricher otpEnricher;


  @Test
  void shouldUpdateCellPhoneAndDialCode_whenEnrichData() {
    var stepUpSession = TestHelper.buildStepUpSessionDto();
    Map<AuthFactor, Object> authConfig = new EnumMap<>(AuthFactor.class);
    var otpAuthConfig = OtpAuthConfig.builder()
        .build();
    authConfig.put(AuthFactor.OTP, otpAuthConfig);
    stepUpSession.setAuthConfig(authConfig);
    var profileInfo = TestHelper.buildProfileInfo();
    otpEnricher.enrichProfileData(stepUpSession, profileInfo);
    var finalOtpAuthConfig = objectMapper.convertValue(stepUpSession.getAuthConfig().get(OTP),
        OtpAuthConfig.class);

    Assertions.assertNotNull(
        finalOtpAuthConfig.getDialCode());
    Assertions.assertNotNull(
        finalOtpAuthConfig.getCellphone());
  }

}