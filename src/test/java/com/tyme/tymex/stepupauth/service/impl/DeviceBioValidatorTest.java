package com.tyme.tymex.stepupauth.service.impl;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tyme.tymex.stepupauth.config.StepUpConfig;
import com.tyme.tymex.stepupauth.domain.AuthFactor;
import com.tyme.tymex.stepupauth.domain.DeviceBioAuthConfig;
import com.tyme.tymex.stepupauth.domain.DeviceBioEnrollStatus;
import com.tyme.tymex.stepupauth.helper.TestHelper;
import com.tyme.tymex.stepupauth.infra.connector.model.DeviceIdType;
import com.tyme.tymex.stepupauth.repository.LinkDeviceInfoRepo;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Optional;
import com.tyme.tymex.stepupauth.repository.StepUpAuthDeviceBioEnrollmentRepo;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

@ExtendWith(MockitoExtension.class)
class DeviceBioValidatorTest {

  @InjectMocks
  private DeviceBioEligibilityValidator deviceBioValidator;
  @Mock
  private LinkDeviceInfoRepo linkDeviceInfoRepo;
  @Spy
  private StepUpConfig stepUpConfig;
  @Mock
  private StepUpAuthDeviceBioEnrollmentRepo stepUpAuthDeviceBioEnrollmentRepo;
  @Spy
  private ObjectMapper objectMapper = new ObjectMapper();

  @BeforeEach
  public void setUp() {
    stepUpConfig.setDefaultExpiredInSeconds(20);
    stepUpConfig.setLinkDeviceThresholdInDays(10L);
    stepUpConfig.setMaxExpiredInSeconds(10);
    stepUpConfig.setDeviceBioEnrollThresholdInDays(7L);
  }

  @Test
  void shouldCheckGlobalEligibilityAllIneligible_whenNotFoundDbRecord() {
    var stepUpSession = TestHelper.buildStepUpSessionDto();
    var authConfig = TestHelper.buildAuthConfigWithDeviceBio();
    stepUpSession.setAuthConfig(authConfig);
    ReflectionTestUtils.setField(deviceBioValidator, "enableLinkDeviceEligibilityCheck", true);
    when(linkDeviceInfoRepo.findLinkDeviceInfoByProfileIdAndAppId(any(), any())).thenReturn(
        Optional.empty());
    var result = deviceBioValidator.checkGlobalEligibility(stepUpSession);
    Assertions.assertFalse(result);
  }

  @Test
  void shouldCheckGlobalEligibilityThrowException_whenGetDeviceIdNotMatchDbRecord() {
    var stepUpSession = TestHelper.buildStepUpSessionDto();
    var authConfig = TestHelper.buildAuthConfigWithDeviceBio();
    stepUpSession.setAuthConfig(authConfig);
    ReflectionTestUtils.setField(deviceBioValidator, "enableLinkDeviceEligibilityCheck", true);
    when(linkDeviceInfoRepo.findLinkDeviceInfoByProfileIdAndAppId(any(), any())).thenReturn(
        Optional.of(
            TestHelper.buildLinkDeviceInfo("wrongDeviceId", null,
                DeviceIdType.INTERNAL_DEVICE_ID)));
    var result = deviceBioValidator.checkGlobalEligibility(stepUpSession);
    Assertions.assertFalse(result);
  }

  @Test
  void shouldReturnFalse_whenLinkDeviceDateNULL() {
    var stepUpSession = TestHelper.buildStepUpSessionDto();
    var authConfig = TestHelper.buildAuthConfigWithDeviceBio();
    stepUpSession.setAuthConfig(authConfig);
    var deviceInfo = TestHelper.buildLinkDeviceInfo("abc123", null,
        DeviceIdType.INTERNAL_DEVICE_ID);
    ReflectionTestUtils.setField(deviceBioValidator, "enableLinkDeviceEligibilityCheck", true);
    when(linkDeviceInfoRepo.findLinkDeviceInfoByProfileIdAndAppId(any(), any())).thenReturn(
        Optional.of(deviceInfo));
    Assertions.assertFalse(deviceBioValidator.checkGlobalEligibility(stepUpSession));
  }

  @Test
  void shouldCheckGlobalEligibilitySuccess_whenValidLinkDeviceDate() {
    var stepUpSession = TestHelper.buildStepUpSessionDto();
    var authConfig = TestHelper.buildAuthConfigWithDeviceBio();
    var stepUpAuthDeviceBioEnrollment = TestHelper.buildStepUpAuthDeviceBioEnrollment(
        DeviceBioEnrollStatus.ENROLLED);
    stepUpSession.setAuthConfig(authConfig);
    var linkedAtTime = Instant.now().minus(20, ChronoUnit.DAYS).toEpochMilli();
    var deviceInfo = TestHelper.buildLinkDeviceInfo("abc123", linkedAtTime,
        DeviceIdType.INTERNAL_DEVICE_ID);
    ReflectionTestUtils.setField(deviceBioValidator, "enableLinkDeviceEligibilityCheck", true);
    when(linkDeviceInfoRepo.findLinkDeviceInfoByProfileIdAndAppId(any(), any())).thenReturn(
        Optional.of(deviceInfo));
    when(stepUpAuthDeviceBioEnrollmentRepo.findDeviceBioEnrollmentByProfileId(any())).thenReturn(
        Optional.of(stepUpAuthDeviceBioEnrollment));
    deviceBioValidator.checkGlobalEligibility(stepUpSession);
  }

  @Test
  void shouldReturnFalse_whenInvalidLinkDeviceDate() {
    var requestToken = TestHelper.buildStepUpSessionDto();
    var authConfig = TestHelper.buildAuthConfigWithDeviceBio();
    requestToken.setAuthConfig(authConfig);
    var linkedAtTime = Instant.now().minus(9, ChronoUnit.DAYS).toEpochMilli();
    var deviceInfo = TestHelper.buildLinkDeviceInfo("abc123", linkedAtTime,
        DeviceIdType.INTERNAL_DEVICE_ID);
    ReflectionTestUtils.setField(deviceBioValidator, "enableLinkDeviceEligibilityCheck", true);
    when(linkDeviceInfoRepo.findLinkDeviceInfoByProfileIdAndAppId(any(), any())).thenReturn(
        Optional.of(deviceInfo));
    Assertions.assertFalse(deviceBioValidator.checkGlobalEligibility(requestToken));
  }

  @Test
  void shouldReturnFalse_whenInputLinkDeviceDays_andInvalid() {
    var stepUpSession = TestHelper.buildStepUpSessionDto();
    var authConfig = TestHelper.buildAuthConfigWithDeviceBio();
    ((DeviceBioAuthConfig) authConfig.get(AuthFactor.DEVICE_BIO)).setLinkDeviceDays(15L);
    stepUpSession.setAuthConfig(authConfig);
    var linkedAtTime = Instant.now().minus(11, ChronoUnit.DAYS).toEpochMilli();
    var deviceInfo = TestHelper.buildLinkDeviceInfo("abc123", linkedAtTime,
        DeviceIdType.INTERNAL_DEVICE_ID);
    ReflectionTestUtils.setField(deviceBioValidator, "enableLinkDeviceEligibilityCheck", true);
    when(linkDeviceInfoRepo.findLinkDeviceInfoByProfileIdAndAppId(any(), any())).thenReturn(
        Optional.of(deviceInfo));
    Assertions.assertFalse(deviceBioValidator.checkGlobalEligibility(stepUpSession));
  }

  @Test
  void shouldReturnTrue_whenInputLinkDeviceDays_andValid() {
    var stepUpSession = TestHelper.buildStepUpSessionDto();
    var authConfig = TestHelper.buildAuthConfigWithDeviceBio();
    ((DeviceBioAuthConfig) authConfig.get(AuthFactor.DEVICE_BIO)).setLinkDeviceDays(5L);
    ((DeviceBioAuthConfig) authConfig.get(AuthFactor.DEVICE_BIO)).setDeviceBioEnrollDays(-5L);
    var stepUpAuthDeviceBioEnrollment = TestHelper.buildStepUpAuthDeviceBioEnrollment(
        DeviceBioEnrollStatus.ENROLLED);
    stepUpSession.setAuthConfig(authConfig);
    var linkedAtTime = Instant.now().minus(9, ChronoUnit.DAYS).toEpochMilli();
    var deviceInfo = TestHelper.buildLinkDeviceInfo("abc123", linkedAtTime,
        DeviceIdType.INTERNAL_DEVICE_ID);
    ReflectionTestUtils.setField(deviceBioValidator, "enableLinkDeviceEligibilityCheck", true);
    when(linkDeviceInfoRepo.findLinkDeviceInfoByProfileIdAndAppId(any(), any())).thenReturn(
        Optional.of(deviceInfo));
    when(stepUpAuthDeviceBioEnrollmentRepo.findDeviceBioEnrollmentByProfileId(any())).thenReturn(
        Optional.of(stepUpAuthDeviceBioEnrollment));
    Assertions.assertTrue(deviceBioValidator.checkGlobalEligibility(stepUpSession));
  }

  @Test
  void shouldReturnFalse_whenDeviceBioEnrollment_AndIneligibleStatus() {
    var stepUpSession = TestHelper.buildStepUpSessionDto();
    var authConfig = TestHelper.buildAuthConfigWithDeviceBio();
    var stepUpAuthDeviceBioEnrollment = TestHelper.buildStepUpAuthDeviceBioEnrollment(
        DeviceBioEnrollStatus.DISABLED);
    var linkedAtTime = Instant.now().minus(11, ChronoUnit.DAYS).toEpochMilli();

    var deviceInfo = TestHelper.buildLinkDeviceInfo("abc123", linkedAtTime,
        DeviceIdType.INTERNAL_DEVICE_ID);
    ReflectionTestUtils.setField(deviceBioValidator, "enableLinkDeviceEligibilityCheck", true);
    when(linkDeviceInfoRepo.findLinkDeviceInfoByProfileIdAndAppId(any(), any())).thenReturn(
        Optional.of(deviceInfo));
    stepUpSession.setAuthConfig(authConfig);
    when(stepUpAuthDeviceBioEnrollmentRepo.findDeviceBioEnrollmentByProfileId(any())).thenReturn(
        Optional.of(stepUpAuthDeviceBioEnrollment));
    Assertions.assertFalse(deviceBioValidator.checkGlobalEligibility(stepUpSession));
  }

  @Test
  void shouldReturnTrue_whenDeviceBioEnrollmentWithStatusEnrolled() {
    var stepUpSession = TestHelper.buildStepUpSessionDto();
    var authConfig = TestHelper.buildAuthConfigWithDeviceBio();
    var stepUpAuthDeviceBioEnrollment = TestHelper.buildStepUpAuthDeviceBioEnrollment(
        DeviceBioEnrollStatus.ENROLLED);
    var linkedAtTime = Instant.now().minus(11, ChronoUnit.DAYS).toEpochMilli();
    var deviceInfo = TestHelper.buildLinkDeviceInfo("abc123", linkedAtTime,
        DeviceIdType.INTERNAL_DEVICE_ID);
    ((DeviceBioAuthConfig) authConfig.get(AuthFactor.DEVICE_BIO)).setDeviceBioEnrollDays(-7L);
    stepUpSession.setAuthConfig(authConfig);
    ReflectionTestUtils.setField(deviceBioValidator, "enableLinkDeviceEligibilityCheck", true);
    when(linkDeviceInfoRepo.findLinkDeviceInfoByProfileIdAndAppId(any(), any())).thenReturn(
        Optional.of(deviceInfo));
    when(stepUpAuthDeviceBioEnrollmentRepo.findDeviceBioEnrollmentByProfileId(any())).thenReturn(
        Optional.of(stepUpAuthDeviceBioEnrollment));
    Assertions.assertTrue(deviceBioValidator.checkGlobalEligibility(stepUpSession));
  }

  @Test
  void shouldReturnTrue_whenDeviceBioEnrollmentWithStatusReEnrolled() {
    var stepUpSession = TestHelper.buildStepUpSessionDto();
    var authConfig = TestHelper.buildAuthConfigWithDeviceBio();
    var stepUpAuthDeviceBioEnrollment = TestHelper.buildStepUpAuthDeviceBioEnrollment(
        DeviceBioEnrollStatus.RE_ENROLLED);
    var linkedAtTime = Instant.now().minus(11, ChronoUnit.DAYS).toEpochMilli();
    ((DeviceBioAuthConfig) authConfig.get(AuthFactor.DEVICE_BIO)).setDeviceBioEnrollDays(-7L);
    var deviceInfo = TestHelper.buildLinkDeviceInfo("abc123", linkedAtTime,
        DeviceIdType.INTERNAL_DEVICE_ID);
    stepUpSession.setAuthConfig(authConfig);
    ReflectionTestUtils.setField(deviceBioValidator, "enableLinkDeviceEligibilityCheck", true);
    when(linkDeviceInfoRepo.findLinkDeviceInfoByProfileIdAndAppId(any(), any())).thenReturn(
        Optional.of(deviceInfo));
    when(stepUpAuthDeviceBioEnrollmentRepo.findDeviceBioEnrollmentByProfileId(any())).thenReturn(
        Optional.of(stepUpAuthDeviceBioEnrollment));
    Assertions.assertTrue(deviceBioValidator.checkGlobalEligibility(stepUpSession));
  }

  @Test
  void shouldReturnFalse_whenNotFoundEnrollmentDataWithProfileId() {
    var stepUpSession = TestHelper.buildStepUpSessionDto();
    var authConfig = TestHelper.buildAuthConfigWithDeviceBio();
    var linkedAtTime = Instant.now().minus(11, ChronoUnit.DAYS).toEpochMilli();
    var deviceInfo = TestHelper.buildLinkDeviceInfo("abc123", linkedAtTime,
        DeviceIdType.INTERNAL_DEVICE_ID);
    stepUpSession.setAuthConfig(authConfig);
    ReflectionTestUtils.setField(deviceBioValidator, "enableLinkDeviceEligibilityCheck", true);
    when(stepUpAuthDeviceBioEnrollmentRepo.findDeviceBioEnrollmentByProfileId(any())).thenReturn(
        Optional.empty());
    when(linkDeviceInfoRepo.findLinkDeviceInfoByProfileIdAndAppId(any(), any())).thenReturn(
        Optional.of(deviceInfo));
    Assertions.assertFalse(deviceBioValidator.checkGlobalEligibility(stepUpSession));
  }


  @Test
  void shouldCheckGlobalEligibilitySuccess_whenTurnOffLinkDeviceCheck() {
    var requestToken = TestHelper.buildStepUpSessionDto();
    var authConfig = TestHelper.buildAuthConfigWithDeviceBio();
    requestToken.setAuthConfig(authConfig);
    ReflectionTestUtils.setField(deviceBioValidator, "enableLinkDeviceEligibilityCheck", false);
    deviceBioValidator.checkGlobalEligibility(requestToken);
  }

  @Test
  void shouldReturnTrue_whenValidEnrollDay() {
    var stepUpSession = TestHelper.buildStepUpSessionDto();
    var authConfig = TestHelper.buildAuthConfigWithDeviceBio();
    var stepUpAuthDeviceBioEnrollment = TestHelper.buildStepUpAuthDeviceBioEnrollment(
        DeviceBioEnrollStatus.ENROLLED);
    var linkedAtTime = Instant.now().minus(11, ChronoUnit.DAYS).toEpochMilli();
    var deviceBioEnrollAtTime = Instant.now().minus(10, ChronoUnit.DAYS).toEpochMilli();
    stepUpAuthDeviceBioEnrollment.setEventTime(deviceBioEnrollAtTime);
    ((DeviceBioAuthConfig) authConfig.get(AuthFactor.DEVICE_BIO)).setDeviceBioEnrollDays(8L);
    var deviceInfo = TestHelper.buildLinkDeviceInfo("abc123", linkedAtTime,
        DeviceIdType.INTERNAL_DEVICE_ID);
    stepUpSession.setAuthConfig(authConfig);
    ReflectionTestUtils.setField(deviceBioValidator, "enableLinkDeviceEligibilityCheck", true);
    when(linkDeviceInfoRepo.findLinkDeviceInfoByProfileIdAndAppId(any(), any())).thenReturn(
        Optional.of(deviceInfo));
    when(stepUpAuthDeviceBioEnrollmentRepo.findDeviceBioEnrollmentByProfileId(any())).thenReturn(
        Optional.of(stepUpAuthDeviceBioEnrollment));
    Assertions.assertTrue(deviceBioValidator.checkGlobalEligibility(stepUpSession));
  }

  @Test
  void shouldReturnFalse_whenEnrollDayInputIsValidButInEligible() {
    var stepUpSession = TestHelper.buildStepUpSessionDto();
    var authConfig = TestHelper.buildAuthConfigWithDeviceBio();
    var linkedAtTime = Instant.now().minus(11, ChronoUnit.DAYS).toEpochMilli();
    var stepUpAuthDeviceBioEnrollment = TestHelper.buildStepUpAuthDeviceBioEnrollment(
        DeviceBioEnrollStatus.ENROLLED);
    var deviceBioEnrollAtTime = Instant.now().minus(6, ChronoUnit.DAYS).toEpochMilli();
    stepUpAuthDeviceBioEnrollment.setEventTime(deviceBioEnrollAtTime);
    ((DeviceBioAuthConfig) authConfig.get(AuthFactor.DEVICE_BIO)).setDeviceBioEnrollDays(8L);
    var deviceInfo = TestHelper.buildLinkDeviceInfo("abc123", linkedAtTime,
        DeviceIdType.INTERNAL_DEVICE_ID);
    stepUpSession.setAuthConfig(authConfig);
    ReflectionTestUtils.setField(deviceBioValidator, "enableLinkDeviceEligibilityCheck", true);
    when(linkDeviceInfoRepo.findLinkDeviceInfoByProfileIdAndAppId(any(), any())).thenReturn(
        Optional.of(deviceInfo));
    when(stepUpAuthDeviceBioEnrollmentRepo.findDeviceBioEnrollmentByProfileId(any())).thenReturn(
        Optional.of(stepUpAuthDeviceBioEnrollment));
    Assertions.assertFalse(deviceBioValidator.checkGlobalEligibility(stepUpSession));
  }

  @Test
  void shouldReturnTrue_whenNoInputEnrollDay() {
    var stepUpSession = TestHelper.buildStepUpSessionDto();
    var authConfig = TestHelper.buildAuthConfigWithDeviceBio();
    var stepUpAuthDeviceBioEnrollment = TestHelper.buildStepUpAuthDeviceBioEnrollment(
        DeviceBioEnrollStatus.ENROLLED);
    var deviceBioEnrollAtTime = Instant.now().minus(10, ChronoUnit.DAYS).toEpochMilli();
    stepUpAuthDeviceBioEnrollment.setEventTime(deviceBioEnrollAtTime);
    var linkedAtTime = Instant.now().minus(11, ChronoUnit.DAYS).toEpochMilli();
    var deviceInfo = TestHelper.buildLinkDeviceInfo("abc123", linkedAtTime,
        DeviceIdType.INTERNAL_DEVICE_ID);
    stepUpSession.setAuthConfig(authConfig);
    ReflectionTestUtils.setField(deviceBioValidator, "enableLinkDeviceEligibilityCheck", true);
    when(linkDeviceInfoRepo.findLinkDeviceInfoByProfileIdAndAppId(any(), any())).thenReturn(
        Optional.of(deviceInfo));
    when(stepUpAuthDeviceBioEnrollmentRepo.findDeviceBioEnrollmentByProfileId(any())).thenReturn(
        Optional.of(stepUpAuthDeviceBioEnrollment));
    Assertions.assertTrue(deviceBioValidator.checkGlobalEligibility(stepUpSession));
  }
}