package com.tyme.tymex.stepupauth.service.impl;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;


import com.tyme.tymex.stepupauth.helper.TestHelper;
import com.tyme.tymex.stepupauth.infra.dynamodb.domain.StepUpAuthDeviceBioEnrollment;
import com.tyme.tymex.stepupauth.infra.event.DeviceBioEnrollmentDetail;
import com.tyme.tymex.stepupauth.infra.event.Event;
import com.tyme.tymex.stepupauth.repository.StepUpAuthDeviceBioEnrollmentRepo;
import com.tyme.tymex.stepupauth.service.InboxService;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class DeviceBioEnrollmentServiceImplTest {

  @InjectMocks
  private DeviceBioEnrollmentServiceImpl deviceBioEnrollmentService;

  @Mock
  private StepUpAuthDeviceBioEnrollmentRepo stepUpAuthDeviceBioEnrollmentRepo;
  @Mock
  private InboxService inboxService;

  private Event<DeviceBioEnrollmentDetail> event;


  @BeforeEach
  void setUp() {
    event = TestHelper.createDeviceBioEnrollmentEvent();
  }

  @Test
  void shouldRecordNote_whenEventTypeNotSupported() {
    event.setType("INVALID_EVENT");
    deviceBioEnrollmentService.processDeviceBioEnrollmentEvent(event);
    verify(inboxService, times(1)).recordNote(any(), any());
  }

  @Test
  void shouldStoredDb_whenSuccess_AndNoPreviousEnrollment() {
    when(stepUpAuthDeviceBioEnrollmentRepo.findDeviceBioEnrollmentByProfileId(any()))
        .thenReturn(Optional.empty());
    deviceBioEnrollmentService.processDeviceBioEnrollmentEvent(event);
    verify(stepUpAuthDeviceBioEnrollmentRepo, times(1)).saveDeviceBioEnrollment(any());

    verify(inboxService, times(0)).recordNote(any(), any());
  }

  @Test
  void shouldUpdatedDb_whenSuccess_AndHavePreviousEnrollment() {
    when(stepUpAuthDeviceBioEnrollmentRepo.findDeviceBioEnrollmentByProfileId(any()))
        .thenReturn(Optional.of(new StepUpAuthDeviceBioEnrollment()));
    deviceBioEnrollmentService.processDeviceBioEnrollmentEvent(event);
    verify(stepUpAuthDeviceBioEnrollmentRepo, times(1)).saveDeviceBioEnrollment(any());

    verify(inboxService, times(0)).recordNote(any(), any());
  }


}