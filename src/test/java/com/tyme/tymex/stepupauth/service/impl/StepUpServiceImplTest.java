package com.tyme.tymex.stepupauth.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tyme.tymex.stepupauth.config.StepUpConfig;
import com.tyme.tymex.stepupauth.controller.domain.InitStepUpSessionDto;
import com.tyme.tymex.stepupauth.controller.domain.InitStepUpSessionDto.AuthFactorRule;
import com.tyme.tymex.stepupauth.domain.AuthFactor;
import com.tyme.tymex.stepupauth.domain.FactorStatus;
import com.tyme.tymex.stepupauth.domain.IdentifierType;
import com.tyme.tymex.stepupauth.domain.StepUpStatus;
import com.tyme.tymex.stepupauth.helper.TestHelper;
import com.tyme.tymex.stepupauth.infra.dynamodb.domain.HistoryTrailDetail;
import com.tyme.tymex.stepupauth.infra.dynamodb.domain.StepUpAuthFactor;
import com.tyme.tymex.stepupauth.infra.dynamodb.domain.StepUpEntity;
import com.tyme.tymex.stepupauth.infra.exception.DomainException;
import com.tyme.tymex.stepupauth.infra.exception.model.ErrorCode;
import com.tyme.tymex.stepupauth.repository.StepUpRepo;
import com.tyme.tymex.stepupauth.service.AuthFactorEligibilityValidator;
import com.tyme.tymex.stepupauth.service.AuthFactorEligibilityValidatorFactory;
import com.tyme.tymex.stepupauth.service.AuthFactorEnricherFactory;
import com.tyme.tymex.stepupauth.service.ProfileService;
import com.tyme.tymex.stepupauth.service.impl.enricher.OtpEnricher;
import com.tyme.tymex.stepupauth.utils.GlobalUtils;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Stream;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;


@ExtendWith(MockitoExtension.class)
class StepUpServiceImplTest {

  @InjectMocks
  private StepUpServiceImpl stepUpService;

  @Mock
  private StepUpRepo stepUpRepo;
  @Mock
  private AuthFactorEligibilityValidatorFactory authFactorEligibilityValidatorFactory;
  @Mock
  AuthFactorEligibilityValidator authFactorEligibilityValidator;
  @Spy
  private ObjectMapper objectMapper = new ObjectMapper();
  @Spy
  private StepUpConfig stepUpConfig = new StepUpConfig();
  @Mock
  private ProfileService profileService;
  @Mock
  private AuthFactorEnricherFactory authFactorEnricherFactory;
  @Mock
  private OtpEnricher otpEnricher;


  @BeforeEach
  public void setUp() {
    objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    stepUpConfig.setDefaultExpiredInSeconds(20);
    stepUpConfig.setLinkDeviceThresholdInDays(10L);
    stepUpConfig.setMaxExpiredInSeconds(10);

  }

  @Test
  void shouldInitSessionSuccess_whenValidLinkDeviceDate() throws Exception {
    AuthFactorRule authFactorRule = new AuthFactorRule();
    authFactorRule.setFactor(AuthFactor.DEVICE_BIO);
    authFactorRule.setFallbackRules(
        List.of(AuthFactorRule.builder().factor(AuthFactor.OTP).build()));
    var initStepUpSessionDto = new InitStepUpSessionDto();
    initStepUpSessionDto.setAuthFactorRules(List.of(authFactorRule));
    initStepUpSessionDto.setIdentifierId("");
    initStepUpSessionDto.setFlowName("");
    initStepUpSessionDto.setAuthConfig(Map.of());

    when(authFactorEligibilityValidatorFactory.getValidator(any())).thenReturn(
        authFactorEligibilityValidator);
    when(authFactorEligibilityValidator.checkGlobalEligibility(any())).thenReturn(true);
    doNothing().when(stepUpRepo).saveStepUp(any());

    var response = stepUpService.initStepUpSession(initStepUpSessionDto);
    assertNotNull(response);
  }

  @Test
  void shouldEnrichData_whenInitStepUp_andFactorOtp_andIdentifierTypeIsProfileId()
      throws JsonProcessingException {
    AuthFactorRule authFactorRule = new AuthFactorRule();
    authFactorRule.setFactor(AuthFactor.OTP);
    var initStepUpDto = TestHelper.buildStepUpSessionDto();
    initStepUpDto.setIdentifierType(IdentifierType.PROFILE_ID);
    initStepUpDto.setAuthFactorRules(List.of(authFactorRule));

    when(profileService.getProfileInfo(any())).thenReturn(TestHelper.buildProfileInfo());
    when(authFactorEligibilityValidatorFactory.getValidator(any())).thenReturn(
        authFactorEligibilityValidator);
    when(authFactorEligibilityValidator.checkGlobalEligibility(any())).thenReturn(true);
    when(authFactorEnricherFactory.getEnricher(any())).thenReturn(otpEnricher);
    doNothing().when(stepUpRepo).saveStepUp(any());

    var response = stepUpService.initStepUpSession(initStepUpDto);
    assertNotNull(response);
    verify(profileService, times(1)).getProfileInfo(any());
    verify(otpEnricher, times(1)).enrichProfileData(any(), any());


  }

  @Test
  void shouldVerifySuccess_whenValidStepUp() {
    var flowId = "flowId";
    var stepUpAuthId = "step-up-token";
    var request = TestHelper.buildStepUpVerificationRequest(flowId, stepUpAuthId, true);

    when(stepUpRepo.queryByAuthId(anyString())).thenReturn(Stream.of(
        StepUpEntity.builder()
            .pk(GlobalUtils.toStepUpSessionPk(stepUpAuthId))
            .sk(GlobalUtils.toStepUpSessionSk(stepUpAuthId))
            .flowId(flowId)
            .expiredTime(Instant.now().plusSeconds(30))
            .stepUpAuthId(stepUpAuthId)
            .verifyTimes(0)
            .maxVerify(1)
            .overallStatus(StepUpStatus.FACTOR_SUCCESS)
            .build()
    ));
    stepUpService.verifyStepUpSession(request);
    verify(stepUpRepo, times(1)).transactUpsertWithRollback(any(), any());
  }

  @Test
  void shouldVerifyFailed_whenNotFoundStepUpRecord() {
    var flowId = "flowId";
    var stepUpAuthId = "step-up-token";
    var request = TestHelper.buildStepUpVerificationRequest(flowId, stepUpAuthId, true);

    when(stepUpRepo.queryByAuthId(anyString())).thenReturn(Stream.empty());

    var ex = Assertions.assertThrows(DomainException.class,
        () -> stepUpService.verifyStepUpSession(request));
    assertEquals(ErrorCode.STEP_UP_SESSION_NOT_EXIST.getMessage(), ex.getMessage());
  }

  @Test
  void shouldVerifyFailed_whenNotStepUpNotMatchFlowId() {
    var flowId = "flowId";
    var stepUpAuthId = "step-up-token";
    var request = TestHelper.buildStepUpVerificationRequest(flowId, stepUpAuthId, true);

    when(stepUpRepo.queryByAuthId(anyString())).thenReturn(Stream.of(
        StepUpEntity.builder()
            .pk(GlobalUtils.toStepUpSessionPk(stepUpAuthId))
            .sk(GlobalUtils.toStepUpSessionSk(stepUpAuthId))
            .flowId("diff_flow_id")
            .expiredTime(Instant.now().plusSeconds(30))
            .stepUpAuthId(stepUpAuthId)
            .verifyTimes(0)
            .maxVerify(1)
            .overallStatus(StepUpStatus.FACTOR_SUCCESS)
            .build()
    ));

    var ex = Assertions.assertThrows(DomainException.class,
        () -> stepUpService.verifyStepUpSession(request));
    assertEquals(ErrorCode.FLOW_ID_NOT_MATCHED.getMessage(), ex.getMessage());
  }

  @Test
  void shouldReturnSuccess_whenValidToken() {
    var request = TestHelper.buildStepUpValidationRequest(AuthFactor.OTP, true);
    var authId = UUID.randomUUID().toString();
    var stepUpEntity = TestHelper.buildStepUpSessionEntity(authId);
    when(stepUpRepo.findStepUpSessionByAuthId(any())).thenReturn(stepUpEntity);
    var response = stepUpService.validateStepUpSession(request);

    assertEquals(authId, response.getStepUpAuthId());
    assertNotNull(response.getConfig());
  }

  @Test
  void shouldReturnInvalidFactorIndex_whenValidateToken() {
    var request = TestHelper.buildStepUpValidationRequest(AuthFactor.DEVICE_BIO, true);
    var authId = UUID.randomUUID().toString();
    var stepUpEntity = TestHelper.buildStepUpSessionEntity(authId);
    when(stepUpRepo.findStepUpSessionByAuthId(any())).thenReturn(stepUpEntity);
    var ex = Assertions.assertThrows(DomainException.class,
        () -> stepUpService.validateStepUpSession(request));
    assertEquals(ErrorCode.AUTH_FACTOR_PROVIDED_INVALID, ex.getErrorCode());
  }

  @Test
  void shouldThrowException_whenStepUpExpired() {
    var request = TestHelper.buildStepUpValidationRequest(AuthFactor.OTP, true);
    var authId = UUID.randomUUID().toString();
    var stepUpEntity = TestHelper.buildStepUpSessionEntity(authId);
    stepUpEntity.setExpiredTime(Instant.now().minusSeconds(10));
    when(stepUpRepo.findStepUpSessionByAuthId(any())).thenReturn(stepUpEntity);
    var ex = Assertions.assertThrows(DomainException.class,
        () -> stepUpService.validateStepUpSession(request));

    assertEquals(ErrorCode.STEP_UP_SESSION_EXPIRED.getMessage(), ex.getMessage());

  }

  @Test
  void getHistoryTrail_success() {
    var authId = "authId";
    when(stepUpRepo.queryByFactorBegins(anyString())).thenReturn(List.of(
        StepUpEntity.builder()
            .factorResult(FactorStatus.SUCCESS)
            .pk(GlobalUtils.toStepUpSessionPk(authId))
            .sk(GlobalUtils.toFactorSk(AuthFactor.DEVICE_BIO))
            .factor(AuthFactor.DEVICE_BIO)
            .verifyTimes(1)
            .maxAttempts(1)
            .factorHistory(List.of(
                HistoryTrailDetail.builder().attempt(1).timestamp(Instant.now())
                    .result(FactorStatus.SUCCESS).build()))
            .build()));

    var result = stepUpService.getHistoryTrail(authId);

    Assertions.assertEquals(1, result.size());
  }

  @Test
  void updateStepUpSession_shouldThrowSessionNotExists() {
    var request = TestHelper.buildStepUpUpdateRequest("sua-1");
    when(stepUpRepo.queryByAuthId(anyString())).thenReturn(Stream.of());
    DomainException ex = Assertions.assertThrows(DomainException.class,
        () -> stepUpService.updateStepUpSession(request));
    Assertions.assertEquals(ErrorCode.STEP_UP_SESSION_NOT_EXIST, ex.getErrorCode());
  }

  @Test
  void updateStepUpSession_shouldThrowSessionExpired() {
    var authId = "sua-1";
    var expiredEntity = TestHelper.buildStepUpSessionEntity(authId);
    expiredEntity.setExpiredTime(Instant.now().minus(Duration.ofMinutes(1)));
    var request = TestHelper.buildStepUpUpdateRequest(authId);
    when(stepUpRepo.queryByAuthId(anyString()))
        .thenReturn(Stream.of(expiredEntity));
    DomainException ex = Assertions.assertThrows(DomainException.class,
        () -> stepUpService.updateStepUpSession(request));
    Assertions.assertEquals(ErrorCode.STEP_UP_SESSION_EXPIRED, ex.getErrorCode());
  }

  @Test
  void updateStepUpSession_shouldThrowSessionCompleted() {
    var authId = "sua-1";
    var completedEntity = TestHelper.buildStepUpSessionEntity(authId);
    completedEntity.setOverallStatus(StepUpStatus.FACTOR_SUCCESS);
    completedEntity.setCurrentFactorId(null);
    completedEntity.setCurrentFactor(null);
    var request = TestHelper.buildStepUpUpdateRequest(authId);
    when(stepUpRepo.queryByAuthId(anyString()))
        .thenReturn(Stream.of(completedEntity));
    DomainException ex = Assertions.assertThrows(DomainException.class,
        () -> stepUpService.updateStepUpSession(request));
    Assertions.assertEquals(ErrorCode.STEP_UP_SESSION_COMPLETED, ex.getErrorCode());
  }

  @Test
  void updateStepUpSession_shouldThrowSessionNotMatched() {
    var authId = "sua-1";
    var notMatchedEntity = TestHelper.buildStepUpSessionEntity(authId);
    notMatchedEntity.setCurrentFactorId(0);
    var request = TestHelper.buildStepUpUpdateRequest(authId);
    when(stepUpRepo.queryByAuthId(anyString()))
        .thenReturn(Stream.of(notMatchedEntity));
    DomainException ex = Assertions.assertThrows(DomainException.class,
        () -> stepUpService.updateStepUpSession(request));
    Assertions.assertEquals(ErrorCode.AUTH_FACTOR_PROVIDED_INVALID, ex.getErrorCode());
  }

  @Test
  void updateStepUpSession_shouldThrowAttemptNotExpected() {
    var authId = "sua-1";
    var sessionEntity = TestHelper.buildStepUpSessionEntity(authId);
    var notExpectedAttemptEntity = TestHelper.buildStepUpFactorEntity(authId, AuthFactor.OTP);
    notExpectedAttemptEntity.setAttempt(5);
    var request = TestHelper.buildStepUpUpdateRequest(authId);
    when(stepUpRepo.queryByAuthId(anyString()))
        .thenReturn(Stream.of(sessionEntity, notExpectedAttemptEntity));
    DomainException ex = Assertions.assertThrows(DomainException.class,
        () -> stepUpService.updateStepUpSession(request));
    Assertions.assertEquals(ErrorCode.AUTH_FACTOR_PROVIDED_INVALID, ex.getErrorCode());
  }

  @Test
  void updateStepUpSession_shouldSuccess() {
    var authId = "sua-1";
    var sessionEntity = TestHelper.buildStepUpSessionEntity(authId);
    var factorEntity = TestHelper.buildStepUpFactorEntity(authId, AuthFactor.OTP);
    factorEntity.setFactorHistory(new ArrayList<>());
    factorEntity.setMaxAttempts(2);
    when(stepUpRepo.queryByAuthId(authId)).thenReturn(Stream.of(sessionEntity, factorEntity));
    var request = TestHelper.buildStepUpUpdateRequest(authId);
    request.setMaxAttempts(2);
    doNothing().when(stepUpRepo).transactUpsertWithRollback(any(StepUpEntity[].class));
    Assertions.assertDoesNotThrow(() -> stepUpService.updateStepUpSession(request));
  }

  @Test
  void updateStepUpSession_shouldNextValidFallbackFactorAndSuccess() {
    updateStepUpSession_shouldNextValidFallbackFactorAndSuccess(1, false);
  }

  @Test
  void updateStepUpSession_shouldNextInvalidFallbackFactorAndSuccess() {
    updateStepUpSession_shouldNextValidFallbackFactorAndSuccess(1, true);
  }

  @Test
  void updateStepUpSession_shouldNextValidPrimaryFactorAndSuccess() {
    updateStepUpSession_shouldNextValidFallbackFactorAndSuccess(2, true);
  }

  @Test
  void updateStepUpSession_shouldNextInvalidPrimaryFactorAndSuccess() {
    var authId = "sua-1";
    var sessionEntity = TestHelper.buildStepUpSessionEntity(authId);
    sessionEntity.setAuthFactorRules(
        Map.of(1, StepUpAuthFactor.builder().factor(AuthFactor.OTP).index(1).parentIndex(1).build(),
            2, StepUpAuthFactor.builder().factor(AuthFactor.DEVICE_BIO).index(2).parentIndex(2)
                .build()));
    when(stepUpRepo.queryByAuthId(authId)).thenReturn(Stream.of(sessionEntity));
    var request = TestHelper.buildStepUpUpdateRequest(authId);
    request.setPassed(false);
    doNothing().when(stepUpRepo).transactUpsertWithRollback(any(StepUpEntity[].class));
    Assertions.assertDoesNotThrow(() -> stepUpService.updateStepUpSession(request));
  }

  void updateStepUpSession_shouldNextValidFallbackFactorAndSuccess(int parentIndex,
      boolean passed) {
    var authId = "sua-1";
    var sessionEntity = TestHelper.buildStepUpSessionEntity(authId);
    sessionEntity.setAuthFactorRules(
        Map.of(1, StepUpAuthFactor.builder().factor(AuthFactor.OTP).index(1).parentIndex(1).build(),
            2, StepUpAuthFactor.builder().factor(AuthFactor.DEVICE_BIO).index(2)
                .parentIndex(parentIndex)
                .build()));
    when(stepUpRepo.queryByAuthId(authId)).thenReturn(Stream.of(sessionEntity));
    var request = TestHelper.buildStepUpUpdateRequest(authId);
    request.setPassed(passed);
    doNothing().when(stepUpRepo).transactUpsertWithRollback(any(StepUpEntity[].class));
    Assertions.assertDoesNotThrow(() -> stepUpService.updateStepUpSession(request));
  }
}