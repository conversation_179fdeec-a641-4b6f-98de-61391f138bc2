package com.tyme.tymex.stepupauth.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;


import com.tyme.tymex.stepupauth.helper.TestHelper;
import com.tyme.tymex.stepupauth.infra.dynamodb.domain.Inbox;
import com.tyme.tymex.stepupauth.infra.event.DeviceBioEnrollmentDetail;
import com.tyme.tymex.stepupauth.infra.event.Event;
import com.tyme.tymex.stepupauth.infra.exception.DomainException;
import com.tyme.tymex.stepupauth.infra.exception.model.ErrorCode;
import com.tyme.tymex.stepupauth.repository.InboxRepo;
import com.tyme.tymex.stepupauth.utils.GlobalUtils;
import java.util.Optional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class InboxServiceImplTest {

  @Spy
  private ObjectMapper objectMapper = new ObjectMapper();

  @Mock
  private InboxRepo inboxRepo;

  @InjectMocks
  private InboxServiceImpl inboxService;

  private Event<DeviceBioEnrollmentDetail> event;

  @BeforeEach
  void setUp() {
    event = TestHelper.createDeviceBioEnrollmentEvent();
    inboxService = new InboxServiceImpl(inboxRepo, objectMapper);
  }

  @Test
  void findEventSuccess() {
    inboxService.findEvent(event);
    verify(inboxRepo).findByPk(GlobalUtils.toInboxPk(event.getId()));
  }

  @Test
  void shouldNotWriteEvent_writeEvent_andEventExist() {
    when(inboxRepo.findByPk(any())).thenReturn(Optional.of(new Inbox()));
    inboxService.writeEvent(event);
    verify(inboxRepo).findByPk(GlobalUtils.toInboxPk(event.getId()));
    verify(inboxRepo, times(0)).put(any());
  }

  @Test
  void shouldInsertNewEvent_ifWriteEvent_andEventNotExisted() throws JsonProcessingException {
    when(inboxRepo.findByPk(any())).thenReturn(Optional.empty());
    when(objectMapper.writeValueAsString(any())).thenReturn("test");
    inboxService.writeEvent(event);
    verify(inboxRepo).findByPk(GlobalUtils.toInboxPk(event.getId()));
    verify(inboxRepo, times(1)).put(any());
  }

  @Test
  void shouldUpdate_whenMarksAsRead_andEventFound_andNotRead() {
    Inbox inbox = Inbox.builder()
        .isRead(false)
        .build();
    when(inboxRepo.findByPk(any())).thenReturn(Optional.of(inbox));
    inboxService.markAsRead(event);
    verify(inboxRepo, times(1)).update(any());

  }

  @Test
  void shouldThrowException_whenMarksAsRead_andEventFound_andAlreadyRead() {
    Inbox inbox = Inbox.builder()
        .isRead(true)
        .build();
    when(inboxRepo.findByPk(any())).thenReturn(Optional.of(inbox));
    var ex = Assertions.assertThrows(DomainException.class,
        () -> inboxService.markAsRead(event));
    assertEquals(ErrorCode.EVENT_ALREADY_MARKED_AS_READ.getMessage(), ex.getMessage());
  }


  @Test
  void shouldThrowException_whenMarksAsRead_andEventNotFound() {
    when(inboxRepo.findByPk(any())).thenReturn(Optional.empty());
    var ex = Assertions.assertThrows(DomainException.class,
        () -> inboxService.markAsRead(event));
    assertEquals(ErrorCode.EVENT_NOT_FOUND.getMessage(), ex.getMessage());
  }

  @Test
  void shouldThrowException_whenRecordNote_andEventNotFound() {
    when(inboxRepo.findByPk(any())).thenReturn(Optional.empty());
    var ex = Assertions.assertThrows(DomainException.class,
        () -> inboxService.recordNote(event,"note"));
    assertEquals(ErrorCode.EVENT_NOT_FOUND.getMessage(), ex.getMessage());
  }

  @Test
  void shouldUpdateDb_whenRecordNote_andEventFound() {
    Inbox inbox = Inbox.builder()
        .isRead(true)
        .build();
    when(inboxRepo.findByPk(any())).thenReturn(Optional.of(inbox));
    inboxService.recordNote(event,"note");
    verify(inboxRepo, times(1)).update(any());
  }


}