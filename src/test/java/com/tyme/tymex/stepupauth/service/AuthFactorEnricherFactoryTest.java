package com.tyme.tymex.stepupauth.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

import com.tyme.tymex.stepupauth.domain.AuthFactor;
import com.tyme.tymex.stepupauth.service.impl.enricher.OtpEnricher;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class AuthFactorEnricherFactoryTest {

  private AuthFactorEnricherFactory factory;

  @Mock
  private OtpEnricher otpEnricher;

  @BeforeEach
  void setUp() {
    when(otpEnricher.getAuthFactor()).thenReturn(AuthFactor.OTP);

    factory = new AuthFactorEnricherFactory(
        List.of(otpEnricher));
  }

  @Test
  void getAuthFactorEnricher_whenAuthFactorIsOtp_shouldReturnOtpEnricher() {
    AuthFactorEnricher enricher = factory.getEnricher(AuthFactor.OTP);
    assertEquals(otpEnricher, enricher);
  }

}