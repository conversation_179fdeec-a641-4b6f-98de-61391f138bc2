package com.tyme.tymex.stepupauth.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

import com.tyme.tymex.stepupauth.domain.AuthFactor;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class StepUpAuthFactorEligibilityValidatorFactoryTest {

  private AuthFactorEligibilityValidatorFactory factory;

  @Mock
  private AuthFactorEligibilityValidator deviceBioValidator;

  @BeforeEach
  void setUp() {
    when(deviceBioValidator.getAuthFactor()).thenReturn(AuthFactor.DEVICE_BIO);

    factory = new AuthFactorEligibilityValidatorFactory(
        List.of(deviceBioValidator));
  }

  @Test
  void getAuthFactorValidator__whenAuthFactorIsDeviceBio_shouldReturnDeviceBioValidator() {
    AuthFactorEligibilityValidator validator = factory.getValidator(AuthFactor.DEVICE_BIO);
    assertEquals(deviceBioValidator, validator);
  }

}