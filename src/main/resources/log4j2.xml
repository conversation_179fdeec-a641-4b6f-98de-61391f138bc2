<?xml version="1.0" encoding="UTF-8"?>
<Configuration>
    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.log4j2.ColorConverter"/>
    <SpringProfile name="!local">
        <Properties>
            <Property name="SERVICE_NAME">tc-mx-step-up-auth-svc</Property>
        </Properties>
        <Appenders>
            <Console name="CONSOLE_JSON" target="SYSTEM_OUT">
                <JSONLayout compact="true" eventEol="true" properties="true" stacktraceAsString="true">
                  <KeyValuePair key="timestamp" value="$${date:yyyy-MM-dd'T'HH:mm:ss.SSSZ}"/>
                </JSONLayout>
            </Console>
        </Appenders>
        <Loggers>
            <Root level="info" includeLocation="true">
                <AppenderRef ref="CONSOLE_JSON"/>
            </Root>
        </Loggers>
    </SpringProfile>
    <SpringProfile name="local | test">
        <Appenders>
            <Console name="CONSOLE_LOCAL" target="SYSTEM_OUT">
                <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss} %-5p [%t] %c{-10}:%L - %m%n"/>
            </Console>
        </Appenders>
        <Loggers>
            <Root level="info" includeLocation="true">
                <AppenderRef ref="CONSOLE_LOCAL"/>
            </Root>
        </Loggers>
    </SpringProfile>
</Configuration>