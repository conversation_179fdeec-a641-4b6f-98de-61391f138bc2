package com.tyme.tymex.stepupauth.config;


import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration
@ConfigurationProperties("step-up")
public class StepUpConfig {

  private Integer defaultExpiredInSeconds;
  private Long linkDeviceThresholdInDays;
  private Integer maxExpiredInSeconds;
  private Long deviceBioEnrollThresholdInDays;
}