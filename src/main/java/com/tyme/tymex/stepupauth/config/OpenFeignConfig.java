package com.tyme.tymex.stepupauth.config;

import com.tyme.tymex.stepupauth.infra.connector.exception.FeignErrorDecoder;
import feign.Retryer;
import org.springframework.context.annotation.Bean;
import feign.codec.ErrorDecoder;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OpenFeignConfig {

  @Bean
  public Retryer retryer() {
    return Retryer.NEVER_RETRY;
  }

  @Bean
  public ErrorDecoder errorDecoder() {
    return new FeignErrorDecoder();
  }


}
