package com.tyme.tymex.stepupauth.config;

import java.net.URI;
import lombok.extern.log4j.Log4j2;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.core.env.Environment;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.dynamodb.DynamoDbClient;
import software.amazon.awssdk.services.dynamodb.model.AttributeDefinition;
import software.amazon.awssdk.services.dynamodb.model.BillingMode;
import software.amazon.awssdk.services.dynamodb.model.CreateTableRequest;
import software.amazon.awssdk.services.dynamodb.model.DeleteTableRequest;
import software.amazon.awssdk.services.dynamodb.model.DescribeTableRequest;
import software.amazon.awssdk.services.dynamodb.model.DynamoDbException;
import software.amazon.awssdk.services.dynamodb.model.KeySchemaElement;
import software.amazon.awssdk.services.dynamodb.model.KeyType;
import software.amazon.awssdk.services.dynamodb.model.ScalarAttributeType;
import software.amazon.awssdk.services.sqs.SqsAsyncClient;

@Log4j2
@Configuration
@Profile({"local", "test", "pipeline"})
public class LocalConfig extends BaseConfig{

  public LocalConfig(Environment environment) {
    super(environment);
  }

  @Bean
  public SqsAsyncClient sqsAsyncClient() {
    String region = environment.getProperty("cloud.aws.region.static", this.region);
    String endpointUrl = environment.getProperty("cloud.aws.sqs.endpoint", this.endpoint);

    return SqsAsyncClient.builder()
        .region(Region.of(region))
        .credentialsProvider(DefaultCredentialsProvider.create())
        .endpointOverride(URI.create(endpointUrl)).build();

  }

  @Bean
  public DynamoDbClient localDynamoDbClient() {
    log.info("Create dynamoDbClientTest");
    String region = environment.getProperty("cloud.aws.region.static", this.region);
    String accessKey = environment.getProperty("cloud.aws.credentials.access-key", LOCAL_ACCESS_KEY);
    String secretKey = environment.getProperty("cloud.aws.credentials.secret-key", LOCAL_SECRET_KEY);
    String endpointUrl = environment.getProperty("cloud.aws.dynamodb.endpoint", this.endpoint);

    AwsBasicCredentials awsBasicCredentials = AwsBasicCredentials.create(accessKey, secretKey);
    var dynamoDbClient = DynamoDbClient.builder().region(Region.of(region)).credentialsProvider(
            StaticCredentialsProvider.create(awsBasicCredentials)).endpointOverride(URI.create(endpointUrl))
        .build();

    var existingTables = dynamoDbClient.listTables().tableNames();
    if (this.isProfileActive("local")) {
      if (!existingTables.contains(DynamoDbConfig.STEP_UP_AUTH_TABLE)) {
        createStepUpAuthTable(dynamoDbClient);
      }
      return dynamoDbClient;
    }

    if (existingTables.contains(DynamoDbConfig.STEP_UP_AUTH_TABLE)) {
      dynamoDbClient.deleteTable(DeleteTableRequest.builder().tableName(DynamoDbConfig.STEP_UP_AUTH_TABLE).build());
    }
    createStepUpAuthTable(dynamoDbClient);

    return dynamoDbClient;
  }

  private void createStepUpAuthTable(DynamoDbClient ddb) {
    final String partitionKey = "pk";
    final String sortKey = "sk";

    var partitionDefinition = AttributeDefinition.builder().attributeName(partitionKey).attributeType(
        ScalarAttributeType.S).build();

    var partitionSchema = KeySchemaElement.builder().attributeName(partitionKey).keyType(KeyType.HASH).build();

    var sortDefinition = AttributeDefinition.builder().attributeName(sortKey).attributeType(ScalarAttributeType.S).build();

    var sortSchema = KeySchemaElement.builder().attributeName(sortKey).keyType(KeyType.RANGE).build();

    var request = CreateTableRequest.builder().attributeDefinitions(partitionDefinition, sortDefinition).keySchema(partitionSchema, sortSchema).tableName(DynamoDbConfig.STEP_UP_AUTH_TABLE)
        .billingMode(BillingMode.PAY_PER_REQUEST).build();

    try {
      var dbWaiter = ddb.waiter();
      ddb.createTable(request);
      var tableRequest = DescribeTableRequest.builder().tableName(DynamoDbConfig.STEP_UP_AUTH_TABLE).build();

      // Wait until the Amazon DynamoDB table is created.
      var waiterResponse = dbWaiter.waitUntilTableExists(tableRequest);
      waiterResponse.matched().response().ifPresent(log::info);
    } catch (DynamoDbException e) {
      log.error(e.getMessage());
      System.exit(1);
    }
  }

}
