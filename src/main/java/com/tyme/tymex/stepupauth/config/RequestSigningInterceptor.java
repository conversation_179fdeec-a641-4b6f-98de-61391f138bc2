package com.tyme.tymex.stepupauth.config;

import static software.amazon.awssdk.http.auth.aws.signer.AwsV4FamilyHttpSigner.SERVICE_SIGNING_NAME;

import com.tyme.tymex.stepupauth.infra.exception.model.ErrorCode;
import com.tyme.tymex.stepupauth.infra.exception.model.SystemException;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import java.io.IOException;
import java.net.URI;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.http.ContentStreamProvider;
import software.amazon.awssdk.http.SdkHttpMethod;
import software.amazon.awssdk.http.SdkHttpRequest;
import software.amazon.awssdk.http.auth.aws.signer.AwsV4HttpSigner;
import software.amazon.awssdk.regions.providers.DefaultAwsRegionProviderChain;

@Log4j2
@RequiredArgsConstructor
public class RequestSigningInterceptor implements RequestInterceptor {

  private static final String SIGNING_NAME = "execute-api";
  private final AwsV4HttpSigner signer;
  private final AwsCredentialsProvider awsCredentialsProvider;

  @SneakyThrows
  @Override
  public void apply(RequestTemplate template) {

    var regionProvider = DefaultAwsRegionProviderChain.builder().build();
    var region = regionProvider.getRegion();

    // Copy Feign Request to AWS DefaultRequest
    var signerRequest = SdkHttpRequest.builder()
        .method(SdkHttpMethod.fromValue(template.method()))
        .uri(toAwsRequestUri(template))
        .headers(toAwsHeaders(template.headers()))
        .build();

    var requestPayload = toAwsContent(template.body());

    // Sign it
    var signedRequest = signer.sign(r -> r.identity(awsCredentialsProvider.resolveCredentials())
        .request(signerRequest)
        .payload(requestPayload)
        .putProperty(SERVICE_SIGNING_NAME, SIGNING_NAME)
        .putProperty(AwsV4HttpSigner.REGION_NAME, region.id())
    );

    // Copy back
    template.headers(null);
    template.headers(toFeignHeaders(signedRequest.request().headers()));
    if (template.body() != null && signedRequest.payload().isPresent()) {
      try {
        template.body(signedRequest.payload().get().newStream().readAllBytes(),
            Charset.defaultCharset());
      } catch (IOException e) {
        log.error("error occurred on json serialize", e);
        throw new SystemException(ErrorCode.UNKNOWN_ERROR.getMessage(), e);
      }
    }
  }

  private Map<String, List<String>> toAwsHeaders(Map<String, Collection<String>> headers) {
    return headers.entrySet().stream()
        .map(header -> Map.entry(header.getKey(), new ArrayList<>(header.getValue())))
        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
  }

  private URI toAwsRequestUri(RequestTemplate template) {
    String url = template.feignTarget().url();
    String pathWithParams = template.url();
    return URI.create(url.endsWith("/") ? url : url + "/")
        .resolve(pathWithParams.startsWith("/") ? pathWithParams.substring(1) : pathWithParams);
  }

  private Map<String, Collection<String>> toFeignHeaders(Map<String, List<String>> headers) {
    return headers
        .entrySet()
        .stream()
        .collect(Collectors.toMap(
            Map.Entry::getKey,
            header -> new ArrayList<>(header.getValue())
        ));
  }

  private ContentStreamProvider toAwsContent(byte[] body) {
    return Optional.ofNullable(body)
        .map(b -> RequestBody.fromBytes(b).contentStreamProvider())
        .orElse(RequestBody.empty().contentStreamProvider());
  }
}