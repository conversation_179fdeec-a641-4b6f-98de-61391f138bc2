package com.tyme.tymex.stepupauth.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class InitialConfiguration implements CommandLineRunner {

    @Value("${step-up.link-device-eligibility-check:true}")
    private boolean enableLinkDeviceEligibilityCheck;

    @Override
    public void run(String... args) throws Exception {
        log.info("enableLinkDeviceEligibilityCheck: {}", enableLinkDeviceEligibilityCheck);
    }
}
