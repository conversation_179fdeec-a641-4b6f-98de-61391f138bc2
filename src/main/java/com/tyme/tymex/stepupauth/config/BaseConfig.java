package com.tyme.tymex.stepupauth.config;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

import java.util.stream.Stream;

@Configuration
@RequiredArgsConstructor
@Getter
@Setter
@ToString
public class BaseConfig {

  protected final Environment environment;
  protected static final String LOCAL_ACCESS_KEY = "accesskey";
  protected static final String LOCAL_SECRET_KEY = "secretkey";

  @Value("${spring.cloud.aws.region}")
  public String region;

  @Value("${infra.endpoint:http://localhost:4566}")
  public String endpoint;

  public boolean isProfileActive(String profile) {
    return Stream
        .of(environment.getActiveProfiles())
        .anyMatch(profile::equalsIgnoreCase);
  }
}
