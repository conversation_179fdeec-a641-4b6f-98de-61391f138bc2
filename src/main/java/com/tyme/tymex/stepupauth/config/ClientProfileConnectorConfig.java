package com.tyme.tymex.stepupauth.config;

import static com.tyme.tymex.stepupauth.constant.Constant.CALLER_ID_HEADER;
import static com.tyme.tymex.stepupauth.constant.Constant.HEADER_API_KEY;
import static com.tyme.tymex.stepupauth.constant.Constant.STEP_UP_CALLER;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;

@RequiredArgsConstructor
public class ClientProfileConnectorConfig {

  @Value("${spring.cloud.openfeign.client.config.client-profile-ingress.api-key}")
  private String apiKey;

  @Bean
  public RequestInterceptor requestInterceptor() {
    return (RequestTemplate template) -> {
      template.header(HEADER_API_KEY, apiKey);
      template.header(CALLER_ID_HEADER, STEP_UP_CALLER);
      
    };
  }

}
