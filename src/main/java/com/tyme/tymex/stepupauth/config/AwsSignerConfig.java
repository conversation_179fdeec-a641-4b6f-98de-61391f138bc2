package com.tyme.tymex.stepupauth.config;

import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import software.amazon.awssdk.http.auth.aws.signer.AwsV4HttpSigner;

@RequiredArgsConstructor
@Configuration
public class AwsSignerConfig {

  @Bean
  public AwsV4HttpSigner awsV4HttpSigner() {
    return AwsV4HttpSigner.create();
  }

}