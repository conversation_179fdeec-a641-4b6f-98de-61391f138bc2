package com.tyme.tymex.stepupauth.repository;

import com.tyme.tymex.stepupauth.infra.dynamodb.domain.StepUpEntity;
import java.util.List;
import java.util.stream.Stream;

public interface StepUpRepo {
  void saveStepUp(StepUpEntity stepUpEntity);
  StepUpEntity findStepUpSessionByAuthId(String stepUpAuthId);
  Stream<StepUpEntity> queryByAuthId(String stepUpAuthId);
  List<StepUpEntity> queryByFactorBegins(String stepUpAuthId);
  void transactUpsertWithRollback(StepUpEntity... entities);
}