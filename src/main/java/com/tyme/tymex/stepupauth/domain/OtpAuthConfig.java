package com.tyme.tymex.stepupauth.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

@Setter
@Getter
@Builder
@ToString
@Jacksonized
@AllArgsConstructor
@NoArgsConstructor
public class OtpAuthConfig {

  private String dialCode;
  private String cellphone;

}