package com.tyme.tymex.stepupauth.validation;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tyme.tymex.stepupauth.controller.domain.InitStepUpSessionDto;
import com.tyme.tymex.stepupauth.domain.AuthFactor;
import com.tyme.tymex.stepupauth.domain.IdentifierType;
import com.tyme.tymex.stepupauth.domain.OtpAuthConfig;
import com.tyme.tymex.stepupauth.utils.GlobalUtils;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@RequiredArgsConstructor
public class OtpFactorConfigValidator implements
    ConstraintValidator<ValidOtpFactorConfig, InitStepUpSessionDto> {

  private final ObjectMapper mapper;

  @Override
  public boolean isValid(InitStepUpSessionDto obj, ConstraintValidatorContext context) {
    if (null == GlobalUtils.findFirstRuleByFactor(obj, AuthFactor.OTP)) {
      return true;
    }

    if (obj.getIdentifierType() == IdentifierType.PROFILE_ID) {
      return true;
    }

    if (obj.getAuthConfig() == null) {
      GlobalUtils.buildConstraintViolationWithTemplate(context, "authConfig", "must not be null");
      return false;
    }
    var otpConfigObj = obj.getAuthConfig().get(AuthFactor.OTP);
    if (otpConfigObj == null) {
      GlobalUtils.buildConstraintViolationWithTemplate(context, "authConfig.OTP",
          "must not be null");
      return false;
    }

    var config = mapper.convertValue(otpConfigObj, OtpAuthConfig.class);
    if (StringUtils.isBlank(config.getDialCode())) {
      GlobalUtils.buildConstraintViolationWithTemplate(context, "authConfig.OTP.dialCode",
          "must not be blank");
      return false;
    } else if (StringUtils.isBlank(config.getCellphone())) {
      GlobalUtils.buildConstraintViolationWithTemplate(context, "authConfig.OTP.cellphone",
          "must not be blank");
      return false;
    }
    return true;
  }
}