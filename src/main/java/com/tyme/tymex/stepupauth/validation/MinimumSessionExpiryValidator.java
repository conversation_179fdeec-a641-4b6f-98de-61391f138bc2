package com.tyme.tymex.stepupauth.validation;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.springframework.beans.factory.annotation.Value;

public class MinimumSessionExpiryValidator implements ConstraintValidator<ValidMinimumSessionExpiry, Integer> {

  @Value("${step-up.min-expired-in-seconds}")
  private Integer minSessionExpiredInSeconds;

  @Override
  public boolean isValid(Integer expiredIn, ConstraintValidatorContext context) {
    if (expiredIn == null) {
      return true;
    }
    return expiredIn >= minSessionExpiredInSeconds;
  }
}