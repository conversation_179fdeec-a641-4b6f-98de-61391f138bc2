package com.tyme.tymex.stepupauth.validation;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.springframework.beans.factory.annotation.Value;

public class MaximumSessionExpiryValidator implements ConstraintValidator<ValidMaximumSessionExpiry, Integer> {

  @Value("${step-up.max-expired-in-seconds}")
  private Integer maxSessionExpiredInSeconds;

  @Override
  public boolean isValid(Integer expiredIn, ConstraintValidatorContext context) {
    if (expiredIn == null) {
      return true;
    }
    return expiredIn <= maxSessionExpiredInSeconds;
  }
}