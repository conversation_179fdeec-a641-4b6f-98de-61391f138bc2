package com.tyme.tymex.stepupauth.validation;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

@Documented
@Target({FIELD, TYPE})
@Retention(RUNTIME)
@Constraint(validatedBy = {MaximumSessionExpiryValidator.class})
public @interface ValidMaximumSessionExpiry {

  String message() default "The value must not exceed the maximum session expiry limit";

  Class<?>[] groups() default {};

  Class<? extends Payload>[] payload() default {};
}