package com.tyme.tymex.stepupauth.validation;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

@Documented
@Target({FIELD, TYPE})
@Retention(RUNTIME)
@Constraint(validatedBy = {DeviceBioFactorConfigValidator.class})
public @interface ValidDeviceBioFactorConfig {

  String message() default "Device Biometric factor configuration is invalid";

  Class<?>[] groups() default {};

  Class<? extends Payload>[] payload() default {};
}