package com.tyme.tymex.stepupauth.validation;

import com.tyme.tymex.stepupauth.controller.domain.InitStepUpSessionDto;
import com.tyme.tymex.stepupauth.domain.AuthFactor;
import com.tyme.tymex.stepupauth.domain.IdentifierType;
import com.tyme.tymex.stepupauth.utils.GlobalUtils;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class PasscodeFactorConfigValidator implements ConstraintValidator<ValidPasscodeFactorConfig, InitStepUpSessionDto> {

  @Override
  public boolean isValid(InitStepUpSessionDto obj, ConstraintValidatorContext context) {
    if (null == GlobalUtils.findFirstRuleByFactor(obj, AuthFactor.PASSCODE)) {
      return true;
    }

    if (obj.getIdentifierType() != IdentifierType.PROFILE_ID) {
      GlobalUtils.buildConstraintViolationWithTemplate(context, "identifierType", "must be PROFILE_ID");
      return false;
    }

    return true;
  }
}