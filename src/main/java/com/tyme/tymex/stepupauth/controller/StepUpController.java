package com.tyme.tymex.stepupauth.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tyme.tymex.stepupauth.constant.Constant;
import com.tyme.tymex.stepupauth.controller.domain.HistoryTrailResponse;
import com.tyme.tymex.stepupauth.controller.domain.InitStepUpSessionDto;
import com.tyme.tymex.stepupauth.controller.domain.StepUpSessionResponse;
import com.tyme.tymex.stepupauth.controller.domain.StepUpUpdateRequest;
import com.tyme.tymex.stepupauth.controller.domain.StepUpValidationRequest;
import com.tyme.tymex.stepupauth.controller.domain.StepUpValidationResponse;
import com.tyme.tymex.stepupauth.controller.domain.StepUpVerificationRequest;
import com.tyme.tymex.stepupauth.controller.domain.StepUpVerificationResponse;
import com.tyme.tymex.stepupauth.service.StepUpService;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Log4j2
@RequiredArgsConstructor
@RequestMapping("internal/step-up")
@Validated
public class StepUpController {

  private final StepUpService stepUpService;

  @PutMapping(
      headers = {Constant.APPLICATION_JSON, Constant.APPLICATION_JSON_V1}
  )
  @ResponseStatus(HttpStatus.OK)
  public void updateStepUpSession(@Valid @RequestBody StepUpUpdateRequest request) {
    stepUpService.updateStepUpSession(request);
  }

  @PostMapping(
      value = "/verification",
      headers = {Constant.APPLICATION_JSON, Constant.APPLICATION_JSON_V1}
  )
  @ResponseStatus(HttpStatus.OK)
  public StepUpVerificationResponse stepUpVerification(
      @Valid @RequestBody StepUpVerificationRequest request) {
    return stepUpService.verifyStepUpSession(request);
  }

  @PostMapping(
      value = "/init",
      headers = {Constant.APPLICATION_JSON, Constant.APPLICATION_JSON_V1}
  )
  @ResponseStatus(HttpStatus.CREATED)
  public StepUpSessionResponse initializeStepUpSession(
      @Valid @RequestBody InitStepUpSessionDto request) throws JsonProcessingException {
    return stepUpService.initStepUpSession(request);
  }

  @PostMapping(
      value = "/validation",
      headers = {Constant.APPLICATION_JSON, Constant.APPLICATION_JSON_V1}
  )
  public StepUpValidationResponse validateStepUpSession(
      @RequestBody @Valid StepUpValidationRequest stepUpValidationRequest) {
    return stepUpService.validateStepUpSession(stepUpValidationRequest);
  }

  @GetMapping(
      value = "/history-trail",
      headers = {Constant.APPLICATION_JSON, Constant.APPLICATION_JSON_V1}
  )
  public List<HistoryTrailResponse> getHistoryTrail(
      @RequestParam @Valid @NotBlank String stepUpAuthId) {
    return stepUpService.getHistoryTrail(stepUpAuthId);
  }

}
