package com.tyme.tymex.stepupauth.controller.domain;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

@Setter
@Getter
@Builder
@ToString
@Jacksonized
@AllArgsConstructor
@NoArgsConstructor
public class StepUpVerificationResponse {
  private String result;
  private List<HistoryTrailResponse> historyTrail;
}
