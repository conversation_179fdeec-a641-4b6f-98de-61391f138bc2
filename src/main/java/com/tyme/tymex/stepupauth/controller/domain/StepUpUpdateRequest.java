package com.tyme.tymex.stepupauth.controller.domain;

import com.tyme.tymex.stepupauth.domain.AuthFactor;
import com.tyme.tymex.stepupauth.domain.FactorStatus;
import com.tyme.tymex.stepupauth.domain.StepUpStatus;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class StepUpUpdateRequest {
  @NotBlank
  private String stepUpAuthId;
  @NotNull
  private AuthFactor authFactor;
  @NotNull
  private Boolean passed;
  private Boolean isKeepCurrentFactor;
  @NotNull
  @Min(value = 1)
  private Integer attempts;
  @NotNull
  @Min(value = 1)
  private Integer maxAttempts;
  private String errorCode;

  public FactorStatus getFactorStatus() {
    return Boolean.TRUE.equals(this.passed) ? FactorStatus.SUCCESS : FactorStatus.FAILED;
  }

  public StepUpStatus getStepUpStatus() {
    return Boolean.TRUE.equals(this.passed) ? StepUpStatus.FACTOR_SUCCESS : StepUpStatus.FACTOR_FAILED;
  }
}
