package com.tyme.tymex.stepupauth.controller.domain;

import com.tyme.tymex.stepupauth.domain.AuthFactor;
import com.tyme.tymex.stepupauth.domain.IdentifierType;
import com.tyme.tymex.stepupauth.validation.ValidDeviceBioFactorConfig;
import com.tyme.tymex.stepupauth.validation.ValidMaximumSessionExpiry;
import com.tyme.tymex.stepupauth.validation.ValidMinimumSessionExpiry;
import com.tyme.tymex.stepupauth.validation.ValidOtpFactorConfig;
import com.tyme.tymex.stepupauth.validation.ValidPasscodeFactorConfig;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Setter
@Getter
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@ValidOtpFactorConfig
@ValidDeviceBioFactorConfig
@ValidPasscodeFactorConfig
public class InitStepUpSessionDto {

  @NotEmpty
  private List<@Valid AuthFactorRule> authFactorRules;

  @NotBlank
  private String identifierId;

  @NotNull
  private IdentifierType identifierType;

  @ValidMinimumSessionExpiry
  @ValidMaximumSessionExpiry
  private Integer expiredIn;

  @NotBlank
  private String flowName;

  @NotBlank
  private String flowId;

  @NotBlank
  private String appId;

  private Map<AuthFactor, Object> authConfig;

  @Setter
  @Getter
  @Builder
  @AllArgsConstructor
  @NoArgsConstructor
  public static class AuthFactorRule {

    @NotNull
    private AuthFactor factor;

    private List<@Valid AuthFactorRule> fallbackRules;
  }
}
