package com.tyme.tymex.stepupauth.controller.domain;

import com.tyme.tymex.stepupauth.domain.AuthFactor;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
public class StepUpValidationRequest {

  @NotBlank
  private String stepUpAuthId;

  @NotNull
  private AuthFactor authFactor;

  private Boolean includeFactorConfig;

}
