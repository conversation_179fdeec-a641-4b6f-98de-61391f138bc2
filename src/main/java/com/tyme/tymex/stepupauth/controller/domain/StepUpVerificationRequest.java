package com.tyme.tymex.stepupauth.controller.domain;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

@Setter
@Getter
@Builder
@ToString
@Jacksonized
@AllArgsConstructor
@NoArgsConstructor
public class StepUpVerificationRequest {

  @NotBlank
  private String flowId;
  @NotBlank
  private String stepUpAuthId;

  @Builder.Default
  private boolean isIncludeHistoryTrail = false;

}