package com.tyme.tymex.stepupauth.controller.domain;

import com.tyme.tymex.stepupauth.domain.AuthFactor;
import com.tyme.tymex.stepupauth.domain.FactorStatus;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.jackson.Jacksonized;

@Setter
@Getter
@Builder
@Jacksonized
@AllArgsConstructor
@NoArgsConstructor
public class HistoryTrailResponse {
  private AuthFactor factor;
  private Integer maxAttempts;
  private List<HistoryDetailRecord> history;

  @Data
  @Builder
  public static class HistoryDetailRecord {
    private Integer attempt;
    private FactorStatus result;
    private String errorCode;
    private Long timestamp;
  }
}