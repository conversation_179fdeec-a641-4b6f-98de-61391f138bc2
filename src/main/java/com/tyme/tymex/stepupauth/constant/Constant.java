package com.tyme.tymex.stepupauth.constant;

import lombok.experimental.UtilityClass;

@UtilityClass
public class Constant {

  public final String APPLICATION_JSON = "Accept=application/json";
  public final String APPLICATION_JSON_V1 = "Accept=application/v1+json";
  public final int STARTING_ACTIVE_FACTOR_ID = 1;
  public final int DEFAULT_MAX_VERIFY = 1;
  public final int STARTING_VERIFY_TIMES = 0;
  public final String STEP_UP_AUTH_ID_KEY = "authId";
  public final String FLOW_NAME_KEY = "flowName";
  public final String STEP_UP_STEP_KEY = "step";
  public static final String PROFILE_ID_HEADER = "profile-id";
  public static final String CALLER_ID_HEADER = "Caller-Id";
  public static final String HEADER_API_KEY = "x-api-key";
  public static final String STEP_UP_CALLER = "step-up-auth-svc";

}
