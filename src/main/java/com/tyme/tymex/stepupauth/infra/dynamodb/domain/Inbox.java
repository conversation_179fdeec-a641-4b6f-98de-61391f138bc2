package com.tyme.tymex.stepupauth.infra.dynamodb.domain;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey;

@DynamoDbBean
@Setter
@SuperBuilder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
public class Inbox extends BaseDynamoEntity{

  @Getter(onMethod = @__({
      @DynamoDbAttribute("pk"),
      @DynamoDbPartitionKey
  }))
  private String partitionKey;
  @Getter(onMethod = @__(@DynamoDbAttribute("type")))
  private String type;
  @Getter(onMethod = @__(@DynamoDbAttribute("payload")))
  private String payload;
  @Getter(onMethod = @__(@DynamoDbAttribute("note")))
  private String note;
  @Getter(onMethod = @__(@DynamoDbAttribute("is_read")))
  private boolean isRead;
  @Getter(onMethod = @__(@DynamoDbAttribute("ttl")))
  private Long timeToLive;

}
