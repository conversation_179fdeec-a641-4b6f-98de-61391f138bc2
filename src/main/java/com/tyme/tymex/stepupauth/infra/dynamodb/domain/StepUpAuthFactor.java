package com.tyme.tymex.stepupauth.infra.dynamodb.domain;

import com.tyme.tymex.stepupauth.domain.AuthFactor;
import com.tyme.tymex.stepupauth.domain.FactorType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;

@Setter
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@DynamoDbBean
public class StepUpAuthFactor {
  private Integer index;
  private AuthFactor factor;
  private Integer parentIndex;

  public FactorType getFactorType() {
    return this.parentIndex.equals(this.index) ? FactorType.PRIMARY : FactorType.FALLBACK;
  }
}