package com.tyme.tymex.stepupauth.infra.exception;

import com.tyme.tymex.stepupauth.infra.exception.model.ErrorCode;
import lombok.Getter;

@Getter
public class DomainException extends RuntimeException {

  private final ErrorCode errorCode;

  private final transient Object[] args;

  public DomainException(ErrorCode errorCode, Object... args) {
    super(String.format(errorCode.getMessage(), args));
    this.errorCode = errorCode;
    this.args = args;
  }

  public DomainException(ErrorCode errorCode) {
    super(errorCode.getMessage());
    this.errorCode = errorCode;
    this.args = null;
  }

  public DomainException(ErrorCode errorCode, Throwable cause) {
    super(errorCode.getMessage(), cause);
    this.errorCode = errorCode;
    this.args = null;
  }
}
