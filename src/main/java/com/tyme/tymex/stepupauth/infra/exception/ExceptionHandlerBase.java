package com.tyme.tymex.stepupauth.infra.exception;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tyme.tymex.stepupauth.infra.exception.model.ApiError;
import com.tyme.tymex.stepupauth.infra.exception.model.ApiErrorEntry;
import com.tyme.tymex.stepupauth.infra.exception.model.ErrorCode;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolationException;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@RestControllerAdvice
@Log4j2
@RequiredArgsConstructor
public class ExceptionHandlerBase {

  private final ObjectMapper objectMapper;

  @ResponseStatus(HttpStatus.BAD_REQUEST)
  @ExceptionHandler(MethodArgumentNotValidException.class)
  protected ApiError onMethodArgumentNotValidException(HttpServletRequest request,
      MethodArgumentNotValidException e) {
    List<ApiErrorEntry> apiErrorEntries = e
        .getBindingResult()
        .getAllErrors()
        .stream()
        .map(error -> {
          var violationName = error instanceof FieldError fieldError ? fieldError.getField() : error.getObjectName();
          String message = violationName + StringUtils.SPACE + error.getDefaultMessage();
          return createApiErrorEntry(ErrorCode.INVALID_REQUIRED_FIELDS.toUniversalCode(),
              String.format(ErrorCode.INVALID_REQUIRED_FIELDS.getMessage(), message));
        }).toList();
    log.error(
        "method: onMethodArgumentNotValidException, endpoint: {}, queryString: {}, apiErrorEntries: {}",
        request.getRequestURI(),
        request.getQueryString(),
        parseApiErrorToJsonString(apiErrorEntries),
        e
    );
    return new ApiError(apiErrorEntries);
  }

  @ResponseStatus(HttpStatus.BAD_REQUEST)
  @ExceptionHandler(ConstraintViolationException.class)
  protected ApiError onConstraintViolationException(HttpServletRequest request,
      ConstraintViolationException e) {
    List<ApiErrorEntry> apiErrorEntries = e.getConstraintViolations().stream()
        .map(violation -> {
              String message =
                  violation.getPropertyPath().toString() + StringUtils.SPACE + violation.getMessage();
              return createApiErrorEntry(ErrorCode.INVALID_REQUIRED_FIELDS.toUniversalCode(),
                  String.format(ErrorCode.INVALID_REQUIRED_FIELDS.getMessage(), message));
            }
        )
        .toList();
    log.error(
        "method: onConstraintViolationException, endpoint: {}, queryString: {}, apiErrorEntries: {}",
        request.getRequestURI(),
        request.getQueryString(),
        parseApiErrorToJsonString(apiErrorEntries),
        e
    );
    return new ApiError(apiErrorEntries);
  }

  @ResponseStatus(HttpStatus.BAD_REQUEST)
  @ExceptionHandler(DomainException.class)
  protected ApiError onDomainException(HttpServletRequest request, DomainException exception) {
    return getResponseError("onDomainException",
        request,
        exception.getErrorCode().toUniversalCode(), exception.getMessage(),
        exception);
  }


  @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
  @ExceptionHandler(Exception.class)
  protected ApiError onException(HttpServletRequest request, Exception exception) {
    return getResponseError("onException",
        request,
        ErrorCode.UNKNOWN_ERROR.toUniversalCode(), ErrorCode.UNKNOWN_ERROR.getMessage(), exception);
  }

  private ApiError getResponseError(String methodException,
      HttpServletRequest request,
      String domainCode,
      String message,
      Throwable exception) {
    ApiErrorEntry apiErrorEntry = createApiErrorEntry(domainCode, message);

    log.error("method: {}, endpoint: {}, queryString: {}, apiErrorEntries: {}",
        methodException,
        request.getRequestURI(),
        request.getQueryString(),
        parseApiErrorToJsonString(List.of(apiErrorEntry)),
        exception);

    return new ApiError(apiErrorEntry);
  }

  private ApiErrorEntry createApiErrorEntry(String universalCode, String message) {
    return ApiErrorEntry
        .builder()
        .unifiedErrorCode(universalCode)
        .errorCode(universalCode)
        .errorMessage(message)
        .build();
  }

  private String parseApiErrorToJsonString(List<ApiErrorEntry> apiErrorEntries) {
    try {
      return objectMapper.writeValueAsString(apiErrorEntries);
    } catch (Exception ex) {
      log.error("Error occurred during convert ApiErrorEntry to json string", ex);
      return null;
    }
  }

}
