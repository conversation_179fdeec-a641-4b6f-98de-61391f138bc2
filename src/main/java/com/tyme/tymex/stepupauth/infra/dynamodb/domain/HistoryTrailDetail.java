package com.tyme.tymex.stepupauth.infra.dynamodb.domain;

import com.tyme.tymex.stepupauth.domain.FactorStatus;
import java.time.Instant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;

@Setter
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@DynamoDbBean
public class HistoryTrailDetail {
  private Integer attempt;
  private Integer maxAttempts;
  private FactorStatus result;
  private String errorCode;
  private Instant timestamp;
}