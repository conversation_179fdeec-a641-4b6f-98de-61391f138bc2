package com.tyme.tymex.stepupauth.infra.dynamodb.repo;

import com.tyme.tymex.stepupauth.infra.dynamodb.domain.DynamoKeyType;
import com.tyme.tymex.stepupauth.infra.dynamodb.domain.StepUpEntity;
import com.tyme.tymex.stepupauth.repository.StepUpRepo;
import com.tyme.tymex.stepupauth.utils.GlobalUtils;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Expression;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.model.GetItemEnhancedRequest;
import software.amazon.awssdk.enhanced.dynamodb.model.PutItemEnhancedRequest;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional;
import software.amazon.awssdk.services.dynamodb.DynamoDbClient;
import software.amazon.awssdk.services.dynamodb.model.AttributeValue;
import software.amazon.awssdk.services.dynamodb.model.TransactWriteItem;
import software.amazon.awssdk.services.dynamodb.model.TransactWriteItemsRequest;

@Log4j2
@Component
@RequiredArgsConstructor
public class StepUpRepoImpl implements StepUpRepo {

  private final DynamoDbClient dynamoDbClient;

  private final DynamoDbTable<StepUpEntity> dynamoDbStepUpTable;

  @Override
  public void saveStepUp(StepUpEntity stepUpEntity) {
    dynamoDbStepUpTable.putItem(PutItemEnhancedRequest.builder(StepUpEntity.class)
        .conditionExpression(Expression.builder()
            .expression("(attribute_not_exists(pk) AND attribute_not_exists(sk))").build())
        .item(stepUpEntity).build());
  }

  @Override
  public StepUpEntity findStepUpSessionByAuthId(String stepUpAuthId) {

    return dynamoDbStepUpTable.getItem(
        GetItemEnhancedRequest.builder()
            .key(this.toKeyFromRaw(stepUpAuthId, stepUpAuthId))
            .consistentRead(true)
            .build()
    );
  }

  @Override
  public Stream<StepUpEntity> queryByAuthId(String stepUpAuthId) {
    QueryConditional queryConditional = QueryConditional.keyEqualTo(this.toKeyFromPkRaw(stepUpAuthId));
    return dynamoDbStepUpTable.query(queryBuilder ->
        queryBuilder.consistentRead(true).queryConditional(queryConditional)
    ).items().stream();
  }

  @Override
  public List<StepUpEntity> queryByFactorBegins(String stepUpAuthId) {
    return dynamoDbStepUpTable.query(queryBuilder ->
        queryBuilder.consistentRead(true).queryConditional(QueryConditional.sortBeginsWith(
            Key.builder().partitionValue(GlobalUtils.toStepUpSessionPk(stepUpAuthId))
                .sortValue(DynamoKeyType.FACTOR.getValue()).build()))).items().stream().toList();
  }

  @Override
  public void transactUpsertWithRollback(StepUpEntity... entities) {
    if (entities.length < 2) {
      throw new IllegalArgumentException("At least two entities are required for upsert operation");
    }
    dynamoDbClient.transactWriteItems(TransactWriteItemsRequest.builder()
        .transactItems(Stream.of(entities).map(this::convertToTransactionItem).toList()).build());
  }

  private TransactWriteItem convertToTransactionItem(StepUpEntity entity) {
    var now = Instant.now();
    entity.setCreatedDate(Optional.ofNullable(entity.getCreatedDate()).orElse(now));
    entity.setModifiedDate(now);
    Map<String, AttributeValue> attributeValueMap = dynamoDbStepUpTable.tableSchema()
        .itemToMap(entity, true);
    return TransactWriteItem.builder()
        .put(builder -> builder.tableName(dynamoDbStepUpTable.tableName()).item(attributeValueMap))
        .build();
  }

  private Key toKeyFromRaw(String pkRaw, String skRaw) {
    return toKeyFrom(GlobalUtils.toStepUpSessionPk(pkRaw), GlobalUtils.toStepUpSessionSk(skRaw));
  }

  private Key toKeyFromPkRaw(String raw) {
    return toKeyFromPk(GlobalUtils.toStepUpSessionPk(raw));
  }

  private Key toKeyFrom(String pv, String sv) {
    return Key.builder().partitionValue(pv).sortValue(sv).build();
  }

  private Key toKeyFromPk(String pv) {
    return Key.builder().partitionValue(pv).build();
  }
}