package com.tyme.tymex.stepupauth.infra.dynamodb.domain.converter;

import org.apache.commons.lang3.StringUtils;
import software.amazon.awssdk.enhanced.dynamodb.AttributeConverter;
import software.amazon.awssdk.enhanced.dynamodb.AttributeValueType;
import software.amazon.awssdk.enhanced.dynamodb.EnhancedType;
import software.amazon.awssdk.services.dynamodb.model.AttributeValue;

import java.time.Instant;

public class InstantAsEpochMilliAttributeConverter implements AttributeConverter<Instant> {

    @Override
    public EnhancedType<Instant> type() {
        return EnhancedType.of(Instant.class);
    }

    @Override
    public AttributeValueType attributeValueType() {
        return AttributeValueType.N;
    }

    @Override
    public AttributeValue transformFrom(Instant input) {
        return AttributeValue
                .builder()
                .n(input != null ? String.valueOf(input.toEpochMilli()) : "")
                .build();
    }

    @Override
    public Instant transformTo(AttributeValue input) {
        return Instant.ofEpochMilli(StringUtils.isNotEmpty(input.n()) ? Long.parseLong(input.n()) : null);
    }

}
