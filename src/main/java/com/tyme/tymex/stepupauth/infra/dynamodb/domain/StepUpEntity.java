package com.tyme.tymex.stepupauth.infra.dynamodb.domain;

import com.tyme.tymex.stepupauth.domain.AuthFactor;
import com.tyme.tymex.stepupauth.domain.FactorStatus;
import com.tyme.tymex.stepupauth.domain.FactorType;
import com.tyme.tymex.stepupauth.domain.IdentifierType;
import com.tyme.tymex.stepupauth.domain.StepUpStatus;
import com.tyme.tymex.stepupauth.infra.dynamodb.domain.converter.FactorRuleConfigAttributeConverter;
import com.tyme.tymex.stepupauth.infra.dynamodb.domain.converter.InstantAsEpochMilliAttributeConverter;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbConvertedBy;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey;

@Setter
@Getter
@SuperBuilder(toBuilder = true)
@ToString
@AllArgsConstructor
@NoArgsConstructor
@DynamoDbBean
public class StepUpEntity extends BaseDynamoEntity {

  @Getter(onMethod = @__({
      @DynamoDbAttribute("pk"),
      @DynamoDbPartitionKey
  }))
  private String pk;

  @Getter(onMethod = @__({
      @DynamoDbAttribute("sk"),
      @DynamoDbSortKey
  }))
  private String sk;

  @Getter(onMethod_ = {
      @DynamoDbAttribute("auth_factor_rules")
  })
  private Map<Integer, StepUpAuthFactor> authFactorRules;

  @Getter(onMethod_ = {
      @DynamoDbAttribute("factor_configs"),
      @DynamoDbConvertedBy(FactorRuleConfigAttributeConverter.class)
  })
  private Map<AuthFactor, Object> factorConfigs;

  @Getter(onMethod = @__(@DynamoDbAttribute("overall_status")))
  private StepUpStatus overallStatus;

  @Getter(onMethod = @__(@DynamoDbAttribute("current_factor_id")))
  private Integer currentFactorId;

  @Getter(onMethod = @__(@DynamoDbAttribute("current_factor")))
  private AuthFactor currentFactor;

  @Getter(onMethod = @__(@DynamoDbAttribute("factor")))
  private AuthFactor factor;

  @Getter(onMethod = @__(@DynamoDbAttribute("factor_id")))
  private Integer factorId;

  @Getter(onMethod = @__(@DynamoDbAttribute("factor_result")))
  private FactorStatus factorResult;

  @Getter(onMethod = @__(@DynamoDbAttribute("factor_type")))
  private FactorType factorType;

  @Getter(onMethod = @__(@DynamoDbAttribute("max_attempts")))
  private Integer maxAttempts;

  @Getter(onMethod = @__(@DynamoDbAttribute("attempt")))
  private Integer attempt;

  @Getter(onMethod = @__(@DynamoDbAttribute("factor_history")))
  private List<HistoryTrailDetail> factorHistory;

  @Getter(onMethod = @__(@DynamoDbAttribute("max_verify")))
  private Integer maxVerify;

  @Getter(onMethod = @__(@DynamoDbAttribute("verify_times")))
  private Integer verifyTimes;

  @Getter(onMethod = @__(@DynamoDbAttribute("flow_id")))
  private String flowId;

  @Getter(onMethod = @__(@DynamoDbAttribute("identifier_id")))
  private String identifierId;

  @Getter(onMethod = @__(@DynamoDbAttribute("identifier_type")))
  private IdentifierType identifierType;

  @Getter(onMethod = @__(@DynamoDbAttribute("step_up_auth_id")))
  private String stepUpAuthId;

  @Getter(onMethod = @__(@DynamoDbAttribute("flow_name")))
  private String flowName;

  @Getter(onMethod = @__(@DynamoDbAttribute("app_id")))
  private String appId;

  @Getter(onMethod_ = {
      @DynamoDbAttribute("expired_time"),
      @DynamoDbConvertedBy(InstantAsEpochMilliAttributeConverter.class)
  })
  private Instant expiredTime;

}