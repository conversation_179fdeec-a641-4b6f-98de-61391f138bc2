package com.tyme.tymex.stepupauth.infra.exception.model;

import lombok.Getter;

public enum ErrorCode {

  UNKNOWN_ERROR("000", "Generic/Technical Error"),
  INVALID_REQUIRED_FIELDS("001", "Invalid required fields: %s"),
  STEP_UP_SESSION_EXPIRED("002", "Auth session expired"),
  NOT_PASS_ALL_FACTOR_RULE("003", "Not passed all Auth factors"),
  STEP_UP_TOKEN_NOT_EXISTS_OR_EXPIRED("004", "StepUp token not exists or expired"),
  STEP_UP_TOKEN_NOT_MATCHED("005", "Step up Token does not matched"),
  LINK_DEVICE_ELIGIBILITY_CHECK_FAILED("006", "Eligibility check fail on linked device"),
  INVALID_OTP_AUTH_CONFIG("007", "Invalid data for otp auth factor config"),
  INVALID_PASSCODE_AUTH_CONFIG("008", "Invalid data for passcode auth factor config"),
  FACTOR_STATUS_ALREADY_UPDATED("009", "Factor status has already been updated"),
  REQUEST_IDENTIFIER_INVALID("010", "Invalid request identifier"),
  AUTH_FACTOR_PROVIDED_INVALID("011", "The factor provided is not as expected"),
  INVALID_DEVICE_BIO_AUTH_CONFIG("012", "Invalid data for device bio auth factor config"),
  STEP_UP_SESSION_COMPLETED("013", "Session has completed"),
  STEP_UP_TOKEN_ALREADY_CREATED("014", "Step up token already created"),
  STEP_UP_TOKEN_ALREADY_VERIFIED("015", "Step up token already verified"),
  ALL_FACTORS_ARE_INELIGIBLE("016", "All factors are ineligible"),
  STEP_UP_SESSION_NOT_EXIST("017", "Step up session does not exist"),
  FLOW_ID_NOT_MATCHED("018", "Flow id not matched for step up session"),
  STEP_UP_VERIFY_EXCEED("019", "Maximum verification attempts exceeded for the step-up session"),
  EVENT_FORMAT_INVALID("020", "Event format is invalid"),
  EVENT_NOT_FOUND("021", "Event is not found"),
  EVENT_ALREADY_MARKED_AS_READ("022", "Event is already marked as read"),
  PROFILE_NOT_FOUND("023", "Profile not found");


  private final String value;

  @Getter
  private final String message;

  ErrorCode(String value, String message) {
    this.value = value;
    this.message = message;
  }

  public String toUniversalCode() {
    return String.format("%s%s%s", SystemIdentifier.INTEGRATION_PLATFORM.getCode(),
        ServiceIdentifier.STEP_UP_AUTH.getCode(), value);
  }

}
