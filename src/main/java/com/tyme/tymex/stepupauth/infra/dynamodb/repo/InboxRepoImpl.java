package com.tyme.tymex.stepupauth.infra.dynamodb.repo;

import com.tyme.tymex.stepupauth.infra.dynamodb.domain.Inbox;
import com.tyme.tymex.stepupauth.repository.InboxRepo;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Repository;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;


@Repository
@Log4j2
@RequiredArgsConstructor
public class InboxRepoImpl implements InboxRepo {

  private final DynamoDbTable<Inbox> dynamoInboxTable;

  @Override
  public Optional<Inbox> findByPk(String pK) {
    return Optional.ofNullable(dynamoInboxTable.getItem(r -> r.key(k -> k.partitionValue(pK))));
  }

  @Override
  public void update(Inbox inbox) {
    dynamoInboxTable.updateItem(inbox);
  }

  @Override
  public void put(Inbox inbox) {
    dynamoInboxTable.putItem(inbox);
  }
}
