package com.tyme.tymex.stepupauth.infra.exception.model;


public class UnknownErrorException extends SystemException {

  public UnknownErrorException() {
    super(ErrorCode.UNKNOWN_ERROR.getMessage(), null);
  }

  public UnknownErrorException(Throwable cause) {
    super(ErrorCode.UNKNOWN_ERROR.getMessage(), cause);
  }

  public UnknownErrorException(String message, Throwable cause) {
    super(message, cause);
  }
}
