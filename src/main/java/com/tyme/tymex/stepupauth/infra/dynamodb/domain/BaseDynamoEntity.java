package com.tyme.tymex.stepupauth.infra.dynamodb.domain;

import com.tyme.tymex.stepupauth.infra.dynamodb.domain.converter.InstantAsEpochMilliAttributeConverter;
import java.time.Instant;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import software.amazon.awssdk.enhanced.dynamodb.extensions.annotations.DynamoDbAutoGeneratedTimestampAttribute;
import software.amazon.awssdk.enhanced.dynamodb.mapper.UpdateBehavior;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbConvertedBy;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbUpdateBehavior;

@Getter
@Setter
@SuperBuilder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
public class BaseDynamoEntity {

  @Getter(onMethod_ = {
      @DynamoDbAutoGeneratedTimestampAttribute,
      @DynamoDbAttribute("created_date"),
      @DynamoDbUpdateBehavior(UpdateBehavior.WRITE_IF_NOT_EXISTS),
      @DynamoDbConvertedBy(InstantAsEpochMilliAttributeConverter.class)
  })
  private Instant createdDate;

  @Getter(onMethod_ = {
      @DynamoDbAutoGeneratedTimestampAttribute,
      @DynamoDbAttribute("modified_date"),
      @DynamoDbUpdateBehavior(UpdateBehavior.WRITE_ALWAYS),
      @DynamoDbConvertedBy(InstantAsEpochMilliAttributeConverter.class)
  })
  private Instant modifiedDate;
}

