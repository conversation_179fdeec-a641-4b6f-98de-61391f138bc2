package com.tyme.tymex.stepupauth.infra.connector.exception;

import com.tyme.tymex.stepupauth.infra.exception.model.SystemException;
import lombok.Getter;
import lombok.Setter;
import org.springframework.http.HttpStatus;

@Setter
@Getter
public class InternalMicroServiceException extends SystemException {

  private final HttpStatus httpStatus;

  private final String jsonError;

  public InternalMicroServiceException(HttpStatus httpStatus, String jsonError, Throwable cause) {
    super(jsonError, cause);
    this.httpStatus = httpStatus;
    this.jsonError = jsonError;
  }
}
