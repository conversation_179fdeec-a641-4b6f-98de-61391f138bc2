package com.tyme.tymex.stepupauth.infra.dynamodb.repo;

import com.tyme.tymex.stepupauth.infra.dynamodb.domain.StepUpAuthDeviceBioEnrollment;
import com.tyme.tymex.stepupauth.repository.StepUpAuthDeviceBioEnrollmentRepo;
import com.tyme.tymex.stepupauth.utils.GlobalUtils;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Repository;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.model.GetItemEnhancedRequest;

@Log4j2
@Repository
@RequiredArgsConstructor
public class DeviceBioEnrollmentRepoImpl implements StepUpAuthDeviceBioEnrollmentRepo {

  private final DynamoDbTable<StepUpAuthDeviceBioEnrollment> deviceBioStepUpAuthTable;

  @Override
  public Optional<StepUpAuthDeviceBioEnrollment> findDeviceBioEnrollmentByProfileId(String profileId) {
    return Optional.ofNullable(deviceBioStepUpAuthTable.getItem(
        GetItemEnhancedRequest.builder()
            .key(this.toDeviceBioEnrollmentFromProfileId(profileId))
            .consistentRead(true)
            .build()
    ));
  }


  @Override
  public void saveDeviceBioEnrollment(StepUpAuthDeviceBioEnrollment stepUpAuthDeviceBioEnrollment) {
    deviceBioStepUpAuthTable.updateItem(stepUpAuthDeviceBioEnrollment);
  }

  private Key toKeyFrom(String pv, String sv) {
    return Key.builder().partitionValue(pv).sortValue(sv).build();
  }

  private Key toDeviceBioEnrollmentFromProfileId(String profileId){
    return toKeyFrom(GlobalUtils.toDeviceBioEnrollmentPk(profileId), GlobalUtils.getDeviceBioEnrollmentSk());
  }

}
