package com.tyme.tymex.stepupauth.infra.consumer;

import io.awspring.cloud.sqs.CompletableFutures;
import io.awspring.cloud.sqs.listener.QueueMessageVisibility;
import io.awspring.cloud.sqs.listener.SqsHeaders;
import io.awspring.cloud.sqs.listener.SqsHeaders.MessageSystemAttributes;
import io.awspring.cloud.sqs.listener.errorhandler.AsyncErrorHandler;
import java.util.concurrent.CompletableFuture;
import java.util.function.IntUnaryOperator;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.Message;

@Log4j2
public class CustomAsyncSqsErrorHandler implements AsyncErrorHandler<Object> {

  @Value("${config.sqs.retry.backoff-exponent:3}")
  private double backOffExponent;

  @Value("${config.sqs.retry.backoff-multiplier:0.5}")
  private double backOffMultiplier;

  protected IntUnaryOperator nextRetryCalculator = count -> (int) Math.ceil(
      Math.pow(count, backOffExponent) * backOffMultiplier);

  @Override
  public CompletableFuture<Void> handle(Message<Object> message, Throwable throwable) {
    log.error("exception while processing message", throwable);
    var headers = message.getHeaders();
    var visibility = headers.get(SqsHeaders.SQS_VISIBILITY_TIMEOUT_HEADER);
    if (visibility instanceof QueueMessageVisibility messageVisibility) {
      var receiveCount = Integer.parseInt(
          (String) headers.getOrDefault(MessageSystemAttributes.SQS_APPROXIMATE_RECEIVE_COUNT, 0));
      var nextRetryInSeconds = nextRetryCalculator.applyAsInt(receiveCount);
      messageVisibility.changeTo(nextRetryInSeconds);
      log.warn("message is processed {} times, visibility extended with {} seconds", receiveCount,
          nextRetryInSeconds);
    }
    return CompletableFutures.failedFuture(throwable);
  }

}

