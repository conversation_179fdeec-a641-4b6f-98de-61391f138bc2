package com.tyme.tymex.stepupauth.infra.dynamodb.domain;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey;

@Setter
@Getter
@SuperBuilder(toBuilder = true)
@ToString
@AllArgsConstructor
@NoArgsConstructor
@DynamoDbBean
public class StepUpAuthDeviceBioEnrollment extends BaseDynamoEntity {

  @Getter(onMethod = @__({
      @DynamoDbAttribute("pk"),
      @DynamoDbPartitionKey
  }))
  private String pk;

  @Getter(onMethod = @__({
      @DynamoDbAttribute("sk"),
      @DynamoDbSortKey
  }))
  private String sk;

  @Getter(onMethod = @__(@DynamoDbAttribute("device_id")))
  private String deviceId;

  @Getter(onMethod = @__(@DynamoDbAttribute("channel")))
  private String channel;

  @Getter(onMethod = @__(@DynamoDbAttribute("biometric_type")))
  private String biometricType;

  @Getter(onMethod = @__(@DynamoDbAttribute("flow_name")))
  private String flowName;

  @Getter(onMethod = @__(@DynamoDbAttribute("status")))
  private String status;

  @Getter(onMethod = @__(@DynamoDbAttribute("security_level")))
  private String securityLevel;

  @Getter(onMethod = @__(@DynamoDbAttribute("event_time")))
  private Long eventTime;


}
