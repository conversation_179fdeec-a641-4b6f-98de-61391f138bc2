package com.tyme.tymex.stepupauth.infra.exception.model;


import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.jackson.Jacksonized;

@Getter
@Setter
@Builder
@Jacksonized
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ApiErrorEntry {
  private String unifiedErrorCode;
  private String errorCode;
  private String errorMessage;
}