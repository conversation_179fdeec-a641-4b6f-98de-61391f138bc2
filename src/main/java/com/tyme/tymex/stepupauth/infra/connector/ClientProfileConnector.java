package com.tyme.tymex.stepupauth.infra.connector;


import com.tyme.tymex.stepupauth.config.ClientProfileConnectorConfig;
import com.tyme.tymex.stepupauth.config.RequestSigningInterceptor;
import com.tyme.tymex.stepupauth.constant.Constant;
import com.tyme.tymex.stepupauth.infra.connector.model.ProfileDetailResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(
    name = "client-profile-ingress",
    url = "${spring.cloud.openfeign.client.config.client-profile-ingress.url}",
    configuration = {RequestSigningInterceptor.class, ClientProfileConnectorConfig.class}
)
public interface ClientProfileConnector {

  @GetMapping(value = "/v1/intapi/profiles")
  ProfileDetailResponse getProfileDetail(
      @RequestHeader(value = Constant.PROFILE_ID_HEADER) String profileId);

}