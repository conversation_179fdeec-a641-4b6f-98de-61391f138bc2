package com.tyme.tymex.stepupauth.infra.dynamodb.repo;

import com.tyme.tymex.stepupauth.infra.dynamodb.domain.LinkDeviceInfo;
import com.tyme.tymex.stepupauth.repository.LinkDeviceInfoRepo;
import com.tyme.tymex.stepupauth.utils.GlobalUtils;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Repository;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.model.GetItemEnhancedRequest;

@Repository
@Log4j2
@RequiredArgsConstructor
public class LinkDeviceInfoRepoImpl implements LinkDeviceInfoRepo {

  private final DynamoDbTable<LinkDeviceInfo> linkDeviceInfoTable;

  @Override
  public Optional<LinkDeviceInfo> findLinkDeviceInfoByProfileIdAndAppId(String profileId,
      String appId) {

    Key key = Key.builder()
        .partitionValue(GlobalUtils.toLinkDeviceInfoPk(profileId))
        .sortValue(GlobalUtils.toLinkDeviceInfoSk(appId))
        .build();

    return Optional.ofNullable(linkDeviceInfoTable.getItem(
        GetItemEnhancedRequest.builder()
            .key(key)
            .consistentRead(true)
            .build()
    ));
  }

}
