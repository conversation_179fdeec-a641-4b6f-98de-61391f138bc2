package com.tyme.tymex.stepupauth.infra.event;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
@EqualsAndHashCode(callSuper = false)
public class DeviceBioEnrollmentDetail {

  private String profileId;
  private String deviceId;
  private String channel;
  private String biometricType;
  private Long createdTime;
  private String flowName;
  private String status;
  private String securityLevel;
}
