package com.tyme.tymex.stepupauth.infra.consumer;

import static io.awspring.cloud.sqs.annotation.SqsListenerAcknowledgementMode.ON_SUCCESS;

import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.tyme.tymex.stepupauth.infra.event.DeviceBioEnrollmentDetail;
import com.tyme.tymex.stepupauth.infra.event.Event;
import com.tyme.tymex.stepupauth.service.DeviceBioEnrollmentService;
import io.awspring.cloud.sqs.annotation.SqsListener;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Log4j2
public class DeviceBioEnrollmentSqsConsumer extends
    BaseEventSqsConsumer<DeviceBioEnrollmentDetail> {


  private final JavaType javaType = TypeFactory.defaultInstance().constructParametricType(
      Event.class, DeviceBioEnrollmentDetail.class);

  @Autowired
  private DeviceBioEnrollmentService deviceBioEnrollmentService;

  @Override
  @SqsListener(value = "${spring.cloud.aws.sqs.device-bio-enrollment-sqs}", acknowledgementMode = ON_SUCCESS)
  public void consume(String message) {
    consume(message, javaType);

  }

  @Override
  protected void process(Event<DeviceBioEnrollmentDetail> event) {
    deviceBioEnrollmentService.processDeviceBioEnrollmentEvent(event);
  }
}
