package com.tyme.tymex.stepupauth.infra.dynamodb.domain.converter;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tyme.tymex.stepupauth.domain.AuthFactor;
import java.util.Collections;
import java.util.Map;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import software.amazon.awssdk.enhanced.dynamodb.AttributeConverter;
import software.amazon.awssdk.enhanced.dynamodb.AttributeValueType;
import software.amazon.awssdk.enhanced.dynamodb.EnhancedType;
import software.amazon.awssdk.services.dynamodb.model.AttributeValue;

@Log4j2
public class FactorRuleConfigAttributeConverter implements
    AttributeConverter<Map<AuthFactor, Object>> {

  private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
  public AttributeValue transformFrom(Map<AuthFactor, Object> value) {
    try {
      if (value != null) {
        return AttributeValue.builder().s(objectMapper.writeValueAsString(value)).build();
      }
    } catch (Exception ex){
      log.error(ex.getMessage(), ex);
    }
    return AttributeValue.builder().s(null).build();
  }

  @Override
  public Map<AuthFactor, Object> transformTo(AttributeValue input) {
    try {
      if (input != null && StringUtils.isNotEmpty(input.s())) {
        return objectMapper.readValue(input.s(), new TypeReference<>() {
        });
      }
    } catch (Exception ex) {
      log.error(ex.getMessage(), ex);
    }
    return Collections.emptyMap();
  }

  @Override
  public EnhancedType<Map<AuthFactor, Object>> type() {
    return EnhancedType.mapOf(AuthFactor.class, Object.class);
  }

  @Override
  public AttributeValueType attributeValueType() {
    return AttributeValueType.S;
  }
}