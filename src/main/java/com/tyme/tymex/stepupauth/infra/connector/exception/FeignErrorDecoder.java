package com.tyme.tymex.stepupauth.infra.connector.exception;

import com.tyme.tymex.stepupauth.infra.exception.model.UnknownErrorException;
import feign.FeignException;
import feign.Request;
import feign.Response;
import feign.RetryableException;
import feign.codec.ErrorDecoder;
import lombok.extern.log4j.Log4j2;
import org.apache.logging.log4j.util.Strings;
import org.springframework.http.HttpStatus;

import static feign.FeignException.errorStatus;

@Log4j2
public class FeignErrorDecoder extends ErrorDecoder.Default {

  @Override
  public Exception decode(String methodKey, Response response) {

    FeignException feignException = errorStatus(methodKey, response);

    Request request = response.request();
    int responseStatus = response.status();
    HttpStatus httpStatus = HttpStatus.valueOf(responseStatus);

    if (httpStatus.is4xxClientError() || HttpStatus.INTERNAL_SERVER_ERROR.equals(httpStatus)) {
      var jsonResponse = feignException.contentUTF8();
      if (Strings.isBlank(jsonResponse)) {
        throw new UnknownErrorException(feignException);
      }
      return new InternalMicroServiceException(httpStatus, jsonResponse, feignException);
    }

    // Retry the request again if response code is 5xx
    if (httpStatus.is5xxServerError()) {
      return new RetryableException(
          responseStatus,
          feignException.getMessage(),
          request.httpMethod(),
          feignException,
          (Long) null,
          request
      );
    }

    throw new UnknownErrorException(feignException);
  }
}
