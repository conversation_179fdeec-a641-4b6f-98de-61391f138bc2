package com.tyme.tymex.stepupauth.infra.consumer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tyme.tymex.stepupauth.infra.event.Event;
import com.tyme.tymex.stepupauth.infra.exception.DomainException;
import com.tyme.tymex.stepupauth.infra.exception.model.SystemException;
import com.tyme.tymex.stepupauth.service.InboxService;
import jakarta.validation.Validator;
import lombok.extern.log4j.Log4j2;
import com.tyme.tymex.stepupauth.infra.exception.model.ErrorCode;
import org.springframework.beans.factory.annotation.Autowired;

@Log4j2
public abstract class BaseEventSqsConsumer<D> {

  @Autowired
  private ObjectMapper objectMapper;

  @Autowired
  private Validator validator;

  @Autowired
  private InboxService inboxService;

  public abstract void consume(String message);

  protected abstract void process(Event<D> event);

  protected void consume(String message, JavaType javaType) {
    var event = deserialize(message, javaType);
    validateEvent(event);
    var inbox = inboxService.writeEvent(event);
    if (!inbox.isRead()) {
      process(event);
      inboxService.markAsRead(event);
    }
  }

  private Event<D> deserialize(String message, JavaType javaType) {
    try {
      return objectMapper.readValue(message, javaType);
    } catch (JsonProcessingException ex) {
      throw new SystemException("Cannot deserialize the message", ex);
    }
  }

  private void validateEvent(Event<D> event) {
    var violations = validator.validate(event);
    if (!violations.isEmpty()) {
      throw new DomainException(ErrorCode.EVENT_FORMAT_INVALID);
    }
  }

}

