package com.tyme.tymex.stepupauth.service.impl.enricher;

import static com.tyme.tymex.stepupauth.domain.AuthFactor.OTP;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tyme.tymex.stepupauth.controller.domain.InitStepUpSessionDto;
import com.tyme.tymex.stepupauth.domain.AuthFactor;
import com.tyme.tymex.stepupauth.domain.ProfileInfo;
import com.tyme.tymex.stepupauth.service.AuthFactorEnricher;
import java.util.HashMap;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Log4j2
public class OtpEnricher implements AuthFactorEnricher {

  private final ObjectMapper objectMapper;

  @Override
  public void enrichProfileData(InitStepUpSessionDto stepUpSession, ProfileInfo profileInfo) {
    if (stepUpSession.getAuthConfig() == null) {
      stepUpSession.setAuthConfig(new HashMap<>());
    }
    Map<String, String> otpAuthConfig = getOtpAuthConfig(stepUpSession);

    otpAuthConfig.put("cellphone", profileInfo.personPhoneData().phoneNumber());
    otpAuthConfig.put("dialCode", profileInfo.personPhoneData().dialCode());
    stepUpSession.getAuthConfig().put(OTP, otpAuthConfig);
  }

  @Override
  public AuthFactor getAuthFactor() {
    return OTP;
  }

  public Map<String, String> getOtpAuthConfig(InitStepUpSessionDto stepUpSession) {
    Object otpConfigObject = stepUpSession.getAuthConfig().get(OTP);
    if (otpConfigObject == null) {
      return new HashMap<>();
    } else {
      return objectMapper.convertValue(otpConfigObject, new TypeReference<>() {
      });
    }
  }

}
