package com.tyme.tymex.stepupauth.service.aop;

import com.tyme.tymex.stepupauth.constant.Constant;
import com.tyme.tymex.stepupauth.controller.domain.InitStepUpSessionDto;
import com.tyme.tymex.stepupauth.controller.domain.StepUpSessionResponse;
import com.tyme.tymex.stepupauth.controller.domain.StepUpUpdateRequest;
import com.tyme.tymex.stepupauth.controller.domain.StepUpValidationRequest;
import com.tyme.tymex.stepupauth.controller.domain.StepUpVerificationRequest;
import com.tyme.tymex.stepupauth.controller.domain.StepUpVerificationResponse;
import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;
import org.apache.logging.log4j.CloseableThreadContext;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

@Aspect
@Log4j2
@Component
public class StepUpLoggingAspect {

  private static final String STEP_UP_INITIALIZED = "INITIALIZED";
  private static final String STEP_UP_VALIDATED = "VALIDATED";
  private static final String STEP_UP_UPDATED = "UPDATED";
  private static final String STEP_UP_VERIFIED = "VERIFIED";

  @Pointcut(value = "execution (* com.tyme.tymex.stepupauth.controller.StepUpController.initializeStepUpSession(..)) && args(request)")
  public void initStepUpSession(InitStepUpSessionDto request) {
  }

  @Pointcut(value = "execution (* com.tyme.tymex.stepupauth.controller.StepUpController.validateStepUpSession(..)) && args(stepUpValidationRequest)")
  public void validateStepUpSession(StepUpValidationRequest stepUpValidationRequest) {
  }

  @Pointcut(value = "execution (* com.tyme.tymex.stepupauth.controller.StepUpController.updateStepUpSession(..)) && args(request)")
  public void updateStepUpSession(StepUpUpdateRequest request) {
  }

  @Pointcut(value = "execution (* com.tyme.tymex.stepupauth.controller.StepUpController.stepUpVerification(..)) && args(request)")
  public void stepUpVerification(StepUpVerificationRequest request) {
  }

  @Around(value = "initStepUpSession(request)", argNames = "proceedingJoinPoint,request")
  @SneakyThrows
  public Object afterReturningInitStepUpSession(ProceedingJoinPoint proceedingJoinPoint,
      InitStepUpSessionDto request) {
    try (CloseableThreadContext.Instance ignored = CloseableThreadContext.put(
        Constant.FLOW_NAME_KEY, request.getFlowName())) {
      StepUpSessionResponse result = (StepUpSessionResponse) proceedingJoinPoint.proceed();
      ignored.put(Constant.STEP_UP_AUTH_ID_KEY, result.getStepUpAuthId());
      ignored.put(Constant.STEP_UP_STEP_KEY, STEP_UP_INITIALIZED);
      log.info("step up session initiated, factor rules: {}",
          result.getAuthFactorRules());
      return result;
    }
  }

  @Around(value = "validateStepUpSession(stepUpValidationRequest)", argNames = "proceedingJoinPoint,stepUpValidationRequest")
  @SneakyThrows
  public Object aroundValidateStepUpSession(ProceedingJoinPoint proceedingJoinPoint,
      StepUpValidationRequest stepUpValidationRequest) {
    try (CloseableThreadContext.Instance ignored = CloseableThreadContext.put(
        Constant.STEP_UP_AUTH_ID_KEY, stepUpValidationRequest.getStepUpAuthId())) {
      Object result = proceedingJoinPoint.proceed();
      ignored.put(Constant.STEP_UP_STEP_KEY, STEP_UP_VALIDATED);
      log.info("step up session validation, factor: {}", stepUpValidationRequest.getAuthFactor());
      return result;
    }
  }

  @Around(value = "updateStepUpSession(request)", argNames = "proceedingJoinPoint,request")
  @SneakyThrows
  public Object aroundUpdateStepUpSession(ProceedingJoinPoint proceedingJoinPoint,
      StepUpUpdateRequest request) {
    try (CloseableThreadContext.Instance ignored = CloseableThreadContext.put(
        Constant.STEP_UP_AUTH_ID_KEY, request.getStepUpAuthId())) {
      Object result = proceedingJoinPoint.proceed();
      ignored.put(Constant.STEP_UP_STEP_KEY, STEP_UP_UPDATED);
      log.info("step up session update, factor: {}, passed: {}", request.getAuthFactor(),
          request.getPassed());
      return result;
    }
  }

  @Around(value = "stepUpVerification(request)", argNames = "proceedingJoinPoint,request")
  @SneakyThrows
  public Object aroundStepUpVerification(ProceedingJoinPoint proceedingJoinPoint,
      StepUpVerificationRequest request) {
    try (CloseableThreadContext.Instance ignored = CloseableThreadContext.put(
        Constant.STEP_UP_AUTH_ID_KEY, request.getStepUpAuthId())) {
      StepUpVerificationResponse result = (StepUpVerificationResponse) proceedingJoinPoint.proceed();
      ignored.put(Constant.STEP_UP_STEP_KEY, STEP_UP_VERIFIED);
      log.info("step up session verification, result: {}", result.getResult());
      return result;
    }
  }

  @AfterThrowing(value = "execution (* com.tyme.tymex.stepupauth.controller.StepUpController.*(..))", throwing = "throwing")
  public void afterThrowing(JoinPoint joinPoint, Exception throwing) {
    log.error("method {}, step up exception", joinPoint.getSignature().getName(), throwing);
  }
}
