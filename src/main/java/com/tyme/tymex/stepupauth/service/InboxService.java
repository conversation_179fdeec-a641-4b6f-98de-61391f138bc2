package com.tyme.tymex.stepupauth.service;

import com.tyme.tymex.stepupauth.infra.dynamodb.domain.Inbox;
import com.tyme.tymex.stepupauth.infra.event.Event;
import java.util.Optional;

public interface InboxService {

  <T> Optional<Inbox> findEvent(Event<T> message);

  <T> Inbox writeEvent(Event<T> message);

  <T> void markAsRead(Event<T> message);

  <T> void recordNote(Event<T> message, String failedReason);

}
