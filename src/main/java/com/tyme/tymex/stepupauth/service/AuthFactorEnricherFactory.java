package com.tyme.tymex.stepupauth.service;

import com.tyme.tymex.stepupauth.domain.AuthFactor;
import java.util.EnumMap;
import java.util.List;
import org.springframework.stereotype.Component;

@Component
public class AuthFactorEnricherFactory {

  private final EnumMap<AuthFactor, AuthFactorEnricher> enrichers;

  public AuthFactorEnricherFactory(List<AuthFactorEnricher> enricherList) {
    this.enrichers = new EnumMap<>(AuthFactor.class);
    for (AuthFactorEnricher enricher : enricherList) {
      this.enrichers.put(enricher.getAuthFactor(), enricher);
    }
  }

  public AuthFactorEnricher getEnricher(AuthFactor factor) {
    return enrichers.get(factor);
  }
}
