package com.tyme.tymex.stepupauth.service.impl;

import com.tyme.tymex.stepupauth.infra.dynamodb.domain.StepUpAuthDeviceBioEnrollment;
import com.tyme.tymex.stepupauth.infra.event.DeviceBioEnrollmentDetail;
import com.tyme.tymex.stepupauth.infra.event.Event;
import com.tyme.tymex.stepupauth.infra.event.EventType;
import com.tyme.tymex.stepupauth.repository.StepUpAuthDeviceBioEnrollmentRepo;
import com.tyme.tymex.stepupauth.service.DeviceBioEnrollmentService;
import com.tyme.tymex.stepupauth.service.InboxService;
import com.tyme.tymex.stepupauth.utils.GlobalUtils;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.EnumUtils;
import org.springframework.stereotype.Service;

@Service
@Log4j2
@RequiredArgsConstructor
public class DeviceBioEnrollmentServiceImpl implements DeviceBioEnrollmentService {


  private final StepUpAuthDeviceBioEnrollmentRepo stepUpAuthDeviceBioEnrollmentRepo;

  private final InboxService inboxService;


  private static final List<EventType> SUPPORTED_EVENT_TYPES = List
      .of(EventType.DEVICE_BIO_ENROLLMENT);

  @Override
  public void processDeviceBioEnrollmentEvent(Event<DeviceBioEnrollmentDetail> event) {
    if (!isEventSupported(event)) {
      log.warn("ignored - message id {} with message type {} is not supported", event.getId(),
          event.getType());
      inboxService.recordNote(event, "event type is not supported for device bio enrollment");
      return;
    }
    log.info("process device bio enrollment event with message id {}", event.getId());

    DeviceBioEnrollmentDetail detail = event.getDetail();

    Optional<StepUpAuthDeviceBioEnrollment> stepUpAuthDeviceBioEnrollmentOpt = stepUpAuthDeviceBioEnrollmentRepo.findDeviceBioEnrollmentByProfileId(
        detail.getProfileId());

    StepUpAuthDeviceBioEnrollment stepUpAuthDeviceBioEnrollment;
    stepUpAuthDeviceBioEnrollment = stepUpAuthDeviceBioEnrollmentOpt.map(
            upAuthDeviceBioEnrollment -> updateDeviceBioEnrollment(
                upAuthDeviceBioEnrollment, detail))
        .orElseGet(() -> createNewDeviceBioEnrollment(detail));

    stepUpAuthDeviceBioEnrollmentRepo.saveDeviceBioEnrollment(stepUpAuthDeviceBioEnrollment);
  }

  private StepUpAuthDeviceBioEnrollment createNewDeviceBioEnrollment(
      DeviceBioEnrollmentDetail detail) {
    return StepUpAuthDeviceBioEnrollment.builder()
        .pk(GlobalUtils.toDeviceBioEnrollmentPk(detail.getProfileId()))
        .sk(GlobalUtils.getDeviceBioEnrollmentSk())
        .deviceId(detail.getDeviceId())
        .channel(detail.getChannel())
        .biometricType(detail.getBiometricType())
        .flowName(detail.getFlowName())
        .status(detail.getStatus())
        .securityLevel(detail.getSecurityLevel())
        .eventTime(detail.getCreatedTime())
        .build();
  }


  private StepUpAuthDeviceBioEnrollment updateDeviceBioEnrollment(
      StepUpAuthDeviceBioEnrollment existingEnrollment,
      DeviceBioEnrollmentDetail detail
  ) {
    existingEnrollment.setDeviceId(detail.getDeviceId());
    existingEnrollment.setChannel(detail.getChannel());
    existingEnrollment.setBiometricType(detail.getBiometricType());
    existingEnrollment.setFlowName(detail.getFlowName());
    existingEnrollment.setStatus(detail.getStatus());
    existingEnrollment.setSecurityLevel(detail.getSecurityLevel());
    existingEnrollment.setEventTime(detail.getCreatedTime());
    return existingEnrollment;
  }


  private boolean isEventSupported(Event<DeviceBioEnrollmentDetail> event) {
    return Optional.ofNullable(EnumUtils.getEnum(EventType.class, event.getType()))
        .map(SUPPORTED_EVENT_TYPES::contains)
        .orElse(false);
  }
}
