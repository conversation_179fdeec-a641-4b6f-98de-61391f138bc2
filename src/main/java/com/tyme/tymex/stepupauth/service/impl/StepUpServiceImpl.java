package com.tyme.tymex.stepupauth.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tyme.tymex.stepupauth.config.StepUpConfig;
import com.tyme.tymex.stepupauth.constant.Constant;
import com.tyme.tymex.stepupauth.controller.domain.HistoryTrailResponse;
import com.tyme.tymex.stepupauth.controller.domain.HistoryTrailResponse.HistoryDetailRecord;
import com.tyme.tymex.stepupauth.controller.domain.InitStepUpSessionDto;
import com.tyme.tymex.stepupauth.controller.domain.InitStepUpSessionDto.AuthFactorRule;
import com.tyme.tymex.stepupauth.controller.domain.StepUpSessionResponse;
import com.tyme.tymex.stepupauth.controller.domain.StepUpUpdateRequest;
import com.tyme.tymex.stepupauth.controller.domain.StepUpValidationRequest;
import com.tyme.tymex.stepupauth.controller.domain.StepUpValidationResponse;
import com.tyme.tymex.stepupauth.controller.domain.StepUpVerificationRequest;
import com.tyme.tymex.stepupauth.controller.domain.StepUpVerificationResponse;
import com.tyme.tymex.stepupauth.domain.AuthFactor;
import com.tyme.tymex.stepupauth.domain.FactorStatus;
import com.tyme.tymex.stepupauth.domain.FactorType;
import com.tyme.tymex.stepupauth.domain.IdentifierType;
import com.tyme.tymex.stepupauth.domain.StepUpStatus;
import com.tyme.tymex.stepupauth.infra.dynamodb.domain.DynamoKeyType;
import com.tyme.tymex.stepupauth.infra.dynamodb.domain.HistoryTrailDetail;
import com.tyme.tymex.stepupauth.infra.dynamodb.domain.StepUpAuthFactor;
import com.tyme.tymex.stepupauth.infra.dynamodb.domain.StepUpEntity;
import com.tyme.tymex.stepupauth.infra.exception.DomainException;
import com.tyme.tymex.stepupauth.infra.exception.model.ErrorCode;
import com.tyme.tymex.stepupauth.infra.exception.model.StepUpSessionExpiredException;
import com.tyme.tymex.stepupauth.repository.StepUpRepo;
import com.tyme.tymex.stepupauth.service.AuthFactorEligibilityValidator;
import com.tyme.tymex.stepupauth.service.AuthFactorEligibilityValidatorFactory;
import com.tyme.tymex.stepupauth.service.AuthFactorEnricher;
import com.tyme.tymex.stepupauth.service.AuthFactorEnricherFactory;
import com.tyme.tymex.stepupauth.service.ProfileService;
import com.tyme.tymex.stepupauth.service.StepUpService;
import com.tyme.tymex.stepupauth.utils.GlobalUtils;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.ThreadContext;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@Log4j2
@RequiredArgsConstructor
public class StepUpServiceImpl implements StepUpService {

  private final ObjectMapper objectMapper;
  private final StepUpConfig stepUpConfig;
  private final AuthFactorEligibilityValidatorFactory authFactorEligibilityValidatorFactory;
  private final AuthFactorEnricherFactory authFactorEnricherFactory;
  private final StepUpRepo stepUpRepo;
  private final ProfileService profileService;

  @Override
  public StepUpSessionResponse initStepUpSession(InitStepUpSessionDto stepUpSessionDto)
      throws JsonProcessingException {

    var authFactorList = getAuthFactorList(stepUpSessionDto);
    if (stepUpSessionDto.getIdentifierType() == IdentifierType.PROFILE_ID) {
      checkProfileAndEnrichData(stepUpSessionDto, authFactorList);
    }
    var ineligibleFactors = checkGlobalEligibility(stepUpSessionDto, authFactorList);
    var stepUpSessionEntity = buildStepUpSessionEntity(stepUpSessionDto, ineligibleFactors);

    stepUpRepo.saveStepUp(stepUpSessionEntity);
    var authFactorRulesResp = convertFlattenFactorsToResponse(
        stepUpSessionEntity.getAuthFactorRules());
    return StepUpSessionResponse.builder()
        .stepUpAuthId(stepUpSessionEntity.getStepUpAuthId())
        .authFactorRules(objectMapper.writeValueAsString(authFactorRulesResp))
        .build();
  }

  public List<AuthFactorRule> convertFlattenFactorsToResponse(
      Map<Integer, StepUpAuthFactor> flattenFactorRules) {
    Map<Integer, List<AuthFactorRule>> fallbackRulesMap = new HashMap<>();
    for (StepUpAuthFactor rule : flattenFactorRules.values()) {
      if (!rule.getIndex().equals(rule.getParentIndex())) {
        fallbackRulesMap.computeIfAbsent(rule.getParentIndex(), k -> new ArrayList<>())
            .add(AuthFactorRule.builder()
                .factor(rule.getFactor())
                .build());
      }
    }
    List<AuthFactorRule> authFactorRules = new ArrayList<>();
    for (StepUpAuthFactor rule : flattenFactorRules.values()) {
      if (rule.getIndex().equals(rule.getParentIndex())) {
        authFactorRules.add(
            AuthFactorRule.builder()
                .factor(rule.getFactor())
                .fallbackRules(
                    Optional.ofNullable(fallbackRulesMap.get(rule.getIndex())).orElse(List.of()))
                .build()
        );
      }
    }
    return authFactorRules;
  }

  @Override
  public StepUpVerificationResponse verifyStepUpSession(StepUpVerificationRequest request) {
    List<StepUpEntity> stepUpEntities = stepUpRepo.queryByAuthId(request.getStepUpAuthId())
        .toList();

    StepUpEntity stepUpSessionEntity = stepUpEntities.stream().filter(
            GlobalUtils.filterByAuthId(request.getStepUpAuthId()))
        .findFirst()
        .orElseThrow(() -> new DomainException(ErrorCode.STEP_UP_SESSION_NOT_EXIST));
    ThreadContext.put(Constant.FLOW_NAME_KEY, stepUpSessionEntity.getFlowName());

    validateStepUpSessionExpired(stepUpSessionEntity);

    if (!stepUpSessionEntity.getFlowId().equals(request.getFlowId())) {
      throw new DomainException(ErrorCode.FLOW_ID_NOT_MATCHED);
    }

    StepUpVerificationResponse response = new StepUpVerificationResponse();
    var verifyAttempt = stepUpSessionEntity.getVerifyTimes() + 1;

    if (stepUpSessionEntity.getOverallStatus() == StepUpStatus.FACTOR_SUCCESS) {
      handleSuccessfulStepUpVerification(stepUpSessionEntity, verifyAttempt, request);
      response.setResult(FactorStatus.SUCCESS.name());
    } else {
      response.setResult(FactorStatus.FAILED.name());
    }

    if (Boolean.TRUE.equals(request.isIncludeHistoryTrail())) {
      List<StepUpEntity> factorResultEntities = stepUpEntities.stream().filter(stepUpEntity ->
              stepUpEntity.getSk().startsWith(DynamoKeyType.FACTOR.getValue()))
          .toList();
      response.setHistoryTrail(generateHistoryTrailResp(factorResultEntities));
    }

    return response;
  }

  private void handleSuccessfulStepUpVerification(StepUpEntity stepUpEntity, int verifyAttempt,
      StepUpVerificationRequest request) {
    if (verifyAttempt > stepUpEntity.getMaxVerify()) {
      throw new DomainException(ErrorCode.STEP_UP_VERIFY_EXCEED);
    }
    stepUpEntity.setVerifyTimes(verifyAttempt);
    StepUpEntity verifyRecord = StepUpEntity.builder()
        .pk(stepUpEntity.getPk())
        .sk(GlobalUtils.toVerifyAttemptSk(verifyAttempt))
        .flowId(request.getFlowId())
        .build();
    stepUpRepo.transactUpsertWithRollback(stepUpEntity, verifyRecord);
  }

  @Override
  public StepUpValidationResponse validateStepUpSession(
      StepUpValidationRequest request) {
    StepUpEntity stepUpEntity = Optional.ofNullable(
            stepUpRepo.findStepUpSessionByAuthId(request.getStepUpAuthId()))
        .orElseThrow(() -> new DomainException(ErrorCode.STEP_UP_SESSION_NOT_EXIST));
    validateStepUpSessionExpired(stepUpEntity);
    validateStepUpSessionMatched(request.getAuthFactor(), stepUpEntity);

    StepUpValidationResponse response = StepUpValidationResponse.builder()
        .stepUpAuthId(stepUpEntity.getStepUpAuthId())
        .profileId(IdentifierType.PROFILE_ID == stepUpEntity.getIdentifierType()
            ? stepUpEntity.getIdentifierId() : null)
        .identifierId(stepUpEntity.getIdentifierId())
        .identifierType(stepUpEntity.getIdentifierType().name())
        .flowName(stepUpEntity.getFlowName())
        .build();

    if (Boolean.TRUE.equals(request.getIncludeFactorConfig())) {
      response.setConfig(
          Optional.ofNullable(stepUpEntity.getFactorConfigs())
              .map(authFactor -> authFactor.get(request.getAuthFactor()))
              .orElse(null)
      );
    }

    return response;
  }

  private void validateStepUpSessionMatched(AuthFactor factorName, StepUpEntity stepUpEntity) {
    var currentFactor = this.getCurrentStepUpAuthFactorRule(stepUpEntity);
    if (null == currentFactor) {
      log.error("current factor:{} not found", stepUpEntity.getCurrentFactorId());
      throw new DomainException(ErrorCode.AUTH_FACTOR_PROVIDED_INVALID,
          stepUpEntity.getCurrentFactorId());
    }

    if (!factorName.equals(currentFactor.getFactor())) {
      log.error("provided: {}, expected: {} - id: {}", factorName, currentFactor.getFactor(),
          stepUpEntity.getCurrentFactorId());
      throw new DomainException(ErrorCode.AUTH_FACTOR_PROVIDED_INVALID);
    }
  }

  private void validateStepUpSessionExpired(StepUpEntity stepUpEntity) {
    if (stepUpEntity.getExpiredTime().isBefore(ZonedDateTime.now().toInstant())) {
      log.error("authId {}. Session expired", stepUpEntity.getStepUpAuthId());
      throw new StepUpSessionExpiredException();
    }
  }

  @Override
  public List<HistoryTrailResponse> getHistoryTrail(String stepUpAuthId) {
    log.info("history trail. authId {}", stepUpAuthId);
    var factorResultEntities = stepUpRepo.queryByFactorBegins(stepUpAuthId);
    return generateHistoryTrailResp(factorResultEntities);
  }

  private List<HistoryTrailResponse> generateHistoryTrailResp(
      List<StepUpEntity> factorResultEntities) {
    return factorResultEntities.stream()
        .sorted(Comparator.comparing(StepUpEntity::getFactorId))
        .map(stepUpFactorEntity -> {
          var history = stepUpFactorEntity.getFactorHistory().stream()
              .map(hisTrail -> HistoryDetailRecord.builder()
                  .attempt(hisTrail.getAttempt())
                  .result(hisTrail.getResult())
                  .errorCode(hisTrail.getErrorCode())
                  .timestamp(hisTrail.getTimestamp().toEpochMilli())
                  .build())
              .toList();
          return HistoryTrailResponse.builder()
              .factor(stepUpFactorEntity.getFactor())
              .maxAttempts(stepUpFactorEntity.getMaxAttempts())
              .history(history).build();
        })
        .toList();
  }

  @Override
  public void updateStepUpSession(StepUpUpdateRequest request) {
    Map<String, StepUpEntity> sessionOrFactorMap = stepUpRepo
        .queryByAuthId(request.getStepUpAuthId())
        .filter(GlobalUtils.filterByAuthId(request.getStepUpAuthId())
            .or(GlobalUtils.filterByFactor(request.getAuthFactor())))
        .collect(Collectors.toMap(StepUpEntity::getSk, Function.identity(), (v1, v2) -> v1));

    String sessionSkValue = GlobalUtils.toStepUpSessionSk(request.getStepUpAuthId());
    StepUpEntity sessionEntity = Optional.ofNullable(sessionOrFactorMap.get(sessionSkValue))
        .orElseThrow(() -> new DomainException(ErrorCode.STEP_UP_SESSION_NOT_EXIST));

    String factorSkValue = GlobalUtils.toFactorSk(request.getAuthFactor());

    validateStepUpSessionUpdatable(sessionOrFactorMap, sessionSkValue, factorSkValue, request);

    StepUpEntity factorResultEntity = Optional.ofNullable(sessionOrFactorMap.get(factorSkValue))
        .orElse(StepUpEntity.builder()
            .pk(GlobalUtils.toStepUpSessionPk(sessionEntity.getStepUpAuthId())).sk(factorSkValue)
            .factor(request.getAuthFactor()).factorId(sessionEntity.getCurrentFactorId())
            .factorType(this.getCurrentFactorType(sessionEntity)).attempt(request.getAttempts())
            .maxAttempts(request.getMaxAttempts()).factorHistory(new ArrayList<>()).build());

    fullFillStepUpFactorUpdates(factorResultEntity, request);
    fullFillStepUpSessionUpdates(sessionEntity, request);

    stepUpRepo.transactUpsertWithRollback(sessionEntity, factorResultEntity);
  }

  private void fullFillStepUpSessionUpdates(StepUpEntity entity, StepUpUpdateRequest request) {
    if (Boolean.TRUE.equals(request.getIsKeepCurrentFactor())) {
      return;
    }
    Pair<Integer, StepUpAuthFactor> nextFactorPair = this.lookupNextFactorRule(entity,
        Boolean.TRUE.equals(request.getPassed()));
    entity.setCurrentFactorId(nextFactorPair.getRight() != null ? nextFactorPair.getLeft() : null);
    entity.setCurrentFactor(nextFactorPair.getRight() != null ?
        nextFactorPair.getRight().getFactor() : null);
    entity.setOverallStatus(nextFactorPair.getRight() == null ?
        request.getStepUpStatus() : entity.getOverallStatus());
  }

  /**
   * <p> Factor Rules:
   * <p> ----------------------------------------------------------------
   * <p>  | Factor-Id | Step-Up-Factor-Rule
   * <p>  | 1         | {index: 1, factor: DEVICE_BIO, parentIndex: 1}
   * <p>  | 2         | {index: 2, factor: OTP, parentIndex: 1}
   * <p>  | 3         | {index: 3, factor: FACIAL, parentIndex: 3}
   * <p>  | 4         | {index: 4, factor: OTP, parentIndex: 3}
   * <p> ----------------------------------------------------------------
   * <p>
   * <p> Factor-Type Expectation
   * <p> ----------------------------------------------------------------
   * <p>  | Factor-Id | Factor-Type
   * <p>  | 1         | PRIMARY
   * <p>  | 2         | FALLBACK
   * <p>  | 3         | PRIMARY
   * <p>  | 4         | FALLBACK
   * <p> ----------------------------------------------------------------
   * <p>
   * Logic: Stop looking up a next factor
   * <p> Session Completed: No have any a next available factor
   * <p> Valid Next Primary Factor: PRIMARY type and current factor passed
   * <p> Valid Next Fallback Factor: FALLBACK type and current factor not passed
   */
  private Pair<Integer, StepUpAuthFactor> lookupNextFactorRule(StepUpEntity entity,
      boolean isCurrentFactorPassed) {
    for (var nextFactorId = entity.getCurrentFactorId() + 1; ; nextFactorId++) {
      var nextFactorRule = getStepUpAuthFactorRule(entity, nextFactorId);
      if (nextFactorRule == null ||
          (FactorType.PRIMARY == nextFactorRule.getFactorType() && isCurrentFactorPassed) ||
          (FactorType.FALLBACK == nextFactorRule.getFactorType() && !isCurrentFactorPassed)) {
        return new ImmutablePair<>(nextFactorId, nextFactorRule);
      }
    }
  }

  private void fullFillStepUpFactorUpdates(StepUpEntity entity, StepUpUpdateRequest request) {
    entity.setFactorResult(request.getFactorStatus());
    entity.setAttempt(request.getAttempts());
    entity.setMaxAttempts(request.getMaxAttempts());
    entity.getFactorHistory().add(HistoryTrailDetail.builder().result(request.getFactorStatus())
        .attempt(request.getAttempts()).maxAttempts(request.getMaxAttempts())
        .errorCode(request.getErrorCode()).timestamp(Instant.now()).build());
  }

  private StepUpEntity buildStepUpSessionEntity(InitStepUpSessionDto initStepUpSessionDto,
      Set<AuthFactor> ineligibleFactors) {
    String stepUpAuthId = UUID.randomUUID().toString();
    var expiredIn = Optional.ofNullable(initStepUpSessionDto.getExpiredIn())
        .filter(result -> result <= stepUpConfig.getMaxExpiredInSeconds())
        .orElse(stepUpConfig.getDefaultExpiredInSeconds());
    var authFactorRules = getAuthFactorRules(initStepUpSessionDto, ineligibleFactors);
    return StepUpEntity.builder()
        .pk(GlobalUtils.toStepUpSessionPk(stepUpAuthId))
        .sk(GlobalUtils.toStepUpSessionSk(stepUpAuthId))
        .authFactorRules(authFactorRules)
        .appId(initStepUpSessionDto.getAppId())
        .factorConfigs(initStepUpSessionDto.getAuthConfig())
        .overallStatus(StepUpStatus.IN_PROGRESS)
        .currentFactorId(Constant.STARTING_ACTIVE_FACTOR_ID)
        .currentFactor(authFactorRules.get(Constant.STARTING_ACTIVE_FACTOR_ID).getFactor())
        .flowId(initStepUpSessionDto.getFlowId())
        .identifierId(initStepUpSessionDto.getIdentifierId())
        .identifierType(initStepUpSessionDto.getIdentifierType())
        .stepUpAuthId(stepUpAuthId)
        .flowName(initStepUpSessionDto.getFlowName())
        .expiredTime(Instant.now().plusSeconds(expiredIn))
        .maxVerify(Constant.DEFAULT_MAX_VERIFY)
        .verifyTimes(Constant.STARTING_VERIFY_TIMES)
        .build();
  }

  public Map<Integer, StepUpAuthFactor> getAuthFactorRules(InitStepUpSessionDto stepUpSessionDto,
      Set<AuthFactor> ineligibleFactors) {
    Map<Integer, StepUpAuthFactor> flattenFactorRules = new HashMap<>();
    int currentIndex = 1;
    for (AuthFactorRule parent : stepUpSessionDto.getAuthFactorRules()) {
      Integer parentIndex = currentIndex;
      if (!ineligibleFactors.contains(parent.getFactor())) {
        flattenFactorRules.put(currentIndex, StepUpAuthFactor.builder()
            .index(currentIndex)
            .parentIndex(parentIndex)
            .factor(parent.getFactor())
            .build());
        currentIndex++;
      }
      if (parent.getFallbackRules() != null) {
        for (AuthFactorRule fallbackRule : parent.getFallbackRules()) {
          if (!ineligibleFactors.contains(fallbackRule.getFactor())) {
            flattenFactorRules.put(currentIndex, StepUpAuthFactor.builder()
                .index(currentIndex)
                .parentIndex(parentIndex)
                .factor(fallbackRule.getFactor())
                .build());
            currentIndex++;
          }
        }
      }
    }
    return flattenFactorRules;
  }

  private Set<AuthFactor> getAuthFactorList(InitStepUpSessionDto initStepUpSessionDto) {

    return initStepUpSessionDto.getAuthFactorRules().stream()
        .flatMap(authFactorRule -> {
          Stream<AuthFactor> fallbackStream =
              CollectionUtils.isEmpty(authFactorRule.getFallbackRules())
                  ? Stream.empty()
                  : authFactorRule.getFallbackRules().stream().map(AuthFactorRule::getFactor);
          return Stream.concat(
              fallbackStream,
              Stream.of(authFactorRule.getFactor())
          );
        })
        .collect(Collectors.toSet());
  }

  private void checkProfileAndEnrichData(InitStepUpSessionDto stepUpSession,
      Set<AuthFactor> authFactorList) {
    //TODO clarify if we need to call to profile for all factors or just some factors . This can be a profile check
    var profileData = profileService.getProfileInfo(stepUpSession.getIdentifierId());

    for (AuthFactor factor : authFactorList) {
      AuthFactorEnricher enricher = authFactorEnricherFactory.getEnricher(
          factor);
      if (enricher != null) {
        enricher.enrichProfileData(stepUpSession, profileData);

      }
    }
  }


  private Set<AuthFactor> checkGlobalEligibility(InitStepUpSessionDto initStepUpSessionDto,
      Set<AuthFactor> authFactorList) {
    Set<AuthFactor> ineligibleFactors = new HashSet<>();
    for (AuthFactor factor : authFactorList) {
      AuthFactorEligibilityValidator validator = authFactorEligibilityValidatorFactory.getValidator(
          factor);
      if (validator != null) {
        boolean result = validator.checkGlobalEligibility(initStepUpSessionDto);
        if (!result) {
          ineligibleFactors.add(factor);
        }
      }
    }
    if (ineligibleFactors.size() == authFactorList.size()) {
      throw new DomainException(ErrorCode.ALL_FACTORS_ARE_INELIGIBLE);
    }
    return ineligibleFactors;
  }

  private FactorType getCurrentFactorType(StepUpEntity sessionEntity) {
    return Optional.ofNullable(this.getCurrentStepUpAuthFactorRule(sessionEntity))
        .map(StepUpAuthFactor::getFactorType).orElse(null);
  }

  private StepUpAuthFactor getCurrentStepUpAuthFactorRule(StepUpEntity sessionEntity) {
    return getStepUpAuthFactorRule(sessionEntity, sessionEntity.getCurrentFactorId());
  }

  private StepUpAuthFactor getStepUpAuthFactorRule(StepUpEntity sessionEntity, Integer factorId) {
    if (factorId == null) {
      return null;
    }
    return sessionEntity.getAuthFactorRules().get(factorId);
  }

  private void validateStepUpSessionUpdatable(Map<String, StepUpEntity> sessionOrFactorMap,
      String sessionSkValue, String factorSkValue, StepUpUpdateRequest request) {
    var sessionEntity = sessionOrFactorMap.get(sessionSkValue);
    validateStepUpSessionExpired(sessionEntity);
    if (sessionEntity.getCurrentFactor() == null) {
      log.error("{}. Session completed", sessionEntity.getOverallStatus());
      throw new DomainException(ErrorCode.STEP_UP_SESSION_COMPLETED);
    }

    validateStepUpSessionMatched(request.getAuthFactor(), sessionEntity);

    var factorEntity = sessionOrFactorMap.get(factorSkValue);
    if (factorEntity == null) {
      return;
    }
    if (request.getAttempts() <= factorEntity.getAttempt()) {
      log.error("provided: {}, current: {}. Attempt not expected",
          request.getAttempts(), factorEntity.getAttempt());
      throw new DomainException(ErrorCode.AUTH_FACTOR_PROVIDED_INVALID);
    }
    if (request.getMaxAttempts() != null && !request.getMaxAttempts()
        .equals(factorEntity.getMaxAttempts())) {
      log.error("provided: {}, current: {}. Max attempt not expected",
          request.getAttempts(), factorEntity.getMaxAttempts());
      throw new DomainException(ErrorCode.AUTH_FACTOR_PROVIDED_INVALID);
    }
  }
}