package com.tyme.tymex.stepupauth.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tyme.tymex.stepupauth.controller.domain.HistoryTrailResponse;
import com.tyme.tymex.stepupauth.controller.domain.InitStepUpSessionDto;
import com.tyme.tymex.stepupauth.controller.domain.StepUpSessionResponse;
import com.tyme.tymex.stepupauth.controller.domain.StepUpUpdateRequest;
import com.tyme.tymex.stepupauth.controller.domain.StepUpValidationRequest;
import com.tyme.tymex.stepupauth.controller.domain.StepUpValidationResponse;
import com.tyme.tymex.stepupauth.controller.domain.StepUpVerificationRequest;
import com.tyme.tymex.stepupauth.controller.domain.StepUpVerificationResponse;
import java.util.List;

public interface StepUpService {

  StepUpVerificationResponse verifyStepUpSession(StepUpVerificationRequest request);

  StepUpValidationResponse validateStepUpSession(StepUpValidationRequest request);

  StepUpSessionResponse initStepUpSession(InitStepUpSessionDto stepUpSessionDto)
      throws JsonProcessingException;

  void updateStepUpSession(StepUpUpdateRequest request);

  List<HistoryTrailResponse> getHistoryTrail(String stepUpAuthId);
}