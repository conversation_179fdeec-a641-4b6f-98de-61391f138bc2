package com.tyme.tymex.stepupauth.service;

import com.tyme.tymex.stepupauth.domain.AuthFactor;
import java.util.EnumMap;
import java.util.List;
import org.springframework.stereotype.Component;

@Component
public class AuthFactorEligibilityValidatorFactory {

  private final EnumMap<AuthFactor, AuthFactorEligibilityValidator> validators;

  public AuthFactorEligibilityValidatorFactory(List<AuthFactorEligibilityValidator> validatorList) {
    this.validators = new EnumMap<>(AuthFactor.class);
    for (AuthFactorEligibilityValidator validator : validatorList) {
      this.validators.put(validator.getAuthFactor(), validator);
    }
  }

  public AuthFactorEligibilityValidator getValidator(AuthFactor factor) {
    return validators.get(factor);
  }
}