package com.tyme.tymex.stepupauth.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tyme.tymex.stepupauth.infra.dynamodb.domain.Inbox;
import com.tyme.tymex.stepupauth.infra.event.Event;
import com.tyme.tymex.stepupauth.infra.exception.DomainException;
import com.tyme.tymex.stepupauth.infra.exception.model.ErrorCode;
import com.tyme.tymex.stepupauth.infra.exception.model.SystemException;
import com.tyme.tymex.stepupauth.repository.InboxRepo;
import com.tyme.tymex.stepupauth.service.InboxService;
import com.tyme.tymex.stepupauth.utils.GlobalUtils;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
@Log4j2
@RequiredArgsConstructor
public class InboxServiceImpl implements InboxService {

  private final InboxRepo inboxRepository;


  private final ObjectMapper objectMapper;


  @Override
  public <T> Optional<Inbox> findEvent(Event<T> event) {
    return inboxRepository.findByPk(GlobalUtils.toInboxPk(event.getId()));
  }

  @Override
  public <T> Inbox writeEvent(Event<T> event) {
    return findEvent(event).orElseGet(() -> writeToInbox(event));
  }

  @Override
  public <T> void markAsRead(Event<T> event) {
    final Inbox inbox = findEvent(event)
        .orElseThrow(() -> new DomainException(ErrorCode.EVENT_NOT_FOUND));
    if (inbox.isRead()) {
      throw new DomainException(ErrorCode.EVENT_ALREADY_MARKED_AS_READ, event.getId());
    }
    inbox.setRead(true);
    inboxRepository.update(inbox);
  }

  @Override
  public <T> void recordNote(Event<T> event, String failedReason) {
    var inbox = findEvent(event)
        .orElseThrow(() -> new DomainException(ErrorCode.EVENT_NOT_FOUND));
    inbox.setNote(failedReason);
    inboxRepository.update(inbox);
  }

  private <T> Inbox writeToInbox(Event<T> event) {
    final Inbox inbox;
    try {
      inbox = Inbox.builder()
          .partitionKey(GlobalUtils.toInboxPk(event.getId()))
          .payload(objectMapper.writeValueAsString(event))
          .isRead(false)
          .note(StringUtils.EMPTY)
          .type(event.getType())
          .timeToLive(Instant.now().plus(90, ChronoUnit.DAYS).getEpochSecond())
          .build();
      inboxRepository.put(inbox);
      return inbox;
    } catch (JsonProcessingException ex) {
      log.error("error occurred on json serialize", ex);
      throw new SystemException(ErrorCode.UNKNOWN_ERROR.getMessage(), ex);
    }
  }


}
