package com.tyme.tymex.stepupauth.service.impl;

import com.tyme.tymex.stepupauth.domain.ProfileInfo;
import com.tyme.tymex.stepupauth.infra.connector.ClientProfileConnector;
import com.tyme.tymex.stepupauth.infra.connector.exception.InternalMicroServiceException;
import com.tyme.tymex.stepupauth.infra.connector.model.ProfileDetailResponse;
import com.tyme.tymex.stepupauth.infra.exception.DomainException;
import com.tyme.tymex.stepupauth.infra.exception.model.ErrorCode;
import com.tyme.tymex.stepupauth.service.ProfileService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Log4j2
public class ProfileServiceImpl implements ProfileService {

  private final ClientProfileConnector clientProfileConnector;

  @Override
  public ProfileInfo getProfileInfo(String profileId) {
    ProfileDetailResponse profileDetailResponse;

    try {
      profileDetailResponse = clientProfileConnector.getProfileDetail(
          profileId);
    } catch (InternalMicroServiceException ex) {
      if (ex.getHttpStatus().is4xxClientError()) {
        log.error("4xx error when exchange profile {}", ex.getJsonError());
        throw new DomainException(ErrorCode.PROFILE_NOT_FOUND);
      } else {
        throw ex;
      }
    }

    if (null == profileDetailResponse || profileDetailResponse.id() == null) {
      throw new DomainException(ErrorCode.PROFILE_NOT_FOUND);
    }

    return ProfileInfo.builder()
        .id(profileDetailResponse.id())
        .personPhoneData(profileDetailResponse.personPhoneData())
        .build();
  }
}
